"use client"

import { useEffect, useState } from "react"
import "./publications.css"

export default function PublicationsPage() {
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Set a timeout to hide the loading indicator after the iframe has had time to load
    const timeout = setTimeout(() => {
      setIsLoading(false)
    }, 3000)

    return () => clearTimeout(timeout)
  }, [])

  return (
    <div className="py-12">
      <div className="container mx-auto px-4">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold">Publications</h1>
          <div className="w-24 h-1 bg-[#f5a623] mx-auto mt-4"></div>
          <p className="mt-4 text-gray-600 max-w-2xl mx-auto">
            Complete list of research publications by Professor <PERSON><PERSON><PERSON> in internationally recognized and
            abstracted journals.
          </p>
        </div>

        {/* Publications Container */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-12">
          {isLoading && (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#0a2158] mb-4"></div>
              <p className="text-gray-500">Loading publications...</p>
            </div>
          )}
          <div className="relative" style={{ height: "800px" }}>
            <iframe
              src="https://bibbase.org/service/mendeley/49efd9d1-b84a-3562-b8db-36e64ac7ba81"
              style={{ width: "100%", height: "100%", border: "none" }}
              title="Publications"
              allowFullScreen
            ></iframe>
          </div>
        </div>
      </div>
    </div>
  )
}
