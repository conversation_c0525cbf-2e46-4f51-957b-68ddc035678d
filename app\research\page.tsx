"use client"

import Image from "next/image"
import { <PERSON><PERSON>ext, <PERSON>, BookO<PERSON>, Award } from "lucide-react"
import { motion } from "framer-motion"
import LinkWrapper from "@/components/link-wrapper"

const publications = [
  {
    id: 1,
    title: "Statistical Analysis of COVID-19 Data: Tren<PERSON> and Patterns",
    journal: "Journal of Medical Statistics",
    year: 2022,
    authors: "<PERSON><PERSON>S, <PERSON>, <PERSON>, et al.",
    link: "#",
  },
  {
    id: 2,
    title: "Comparative Study of Survival Analysis Methods in Cancer Research",
    journal: "International Journal of Biostatistics",
    year: 2021,
    authors: "<PERSON><PERSON>, <PERSON>, <PERSON>, et al.",
    link: "#",
  },
  {
    id: 3,
    title: "Application of Machine Learning in Predicting Disease Outcomes",
    journal: "Computational Statistics in Medicine",
    year: 2021,
    authors: "<PERSON>, <PERSON><PERSON>, <PERSON> et al.",
    link: "#",
  },
  {
    id: 4,
    title: "Meta-analysis of Clinical Trials: Methodological Considerations",
    journal: "Statistical Methods in Medical Research",
    year: 2020,
    authors: "<PERSON><PERSON>, <PERSON>, et al.",
    link: "#",
  },
  {
    id: 5,
    title: "Bayesian Approaches to Handling Missing Data in Health Surveys",
    journal: "Journal of Applied Statistics",
    year: 2020,
    authors: "<PERSON><PERSON> NS, <PERSON>, et al.",
    link: "#",
  },
  {
    id: 6,
    title: "Statistical Power Analysis for Clinical Studies: A Practical Guide",
    journal: "Clinical Research Methodology",
    year: 2019,
    authors: "Butt NS, Rahman A, et al.",
    link: "#",
  },
]

const researchAreas = [
  {
    id: 1,
    title: "Biostatistics",
    description: "Statistical methods and their applications in biological and medical research",
    icon: <FileText className="h-10 w-10 text-white" />,
  },
  {
    id: 2,
    title: "Clinical Trials",
    description: "Design, analysis, and interpretation of clinical trials data",
    icon: <Users className="h-10 w-10 text-white" />,
  },
  {
    id: 3,
    title: "Survival Analysis",
    description: "Advanced methods for analyzing time-to-event data in medical research",
    icon: <BookOpen className="h-10 w-10 text-white" />,
  },
  {
    id: 4,
    title: "Statistical Computing",
    description: "Development and application of computational methods for statistical analysis",
    icon: <Award className="h-10 w-10 text-white" />,
  },
]

// Research profiles data
const researchProfiles = [
  {
    id: 1,
    name: "Web of Science",
    image: "/research-profiles/clarivate.png",
    link: "https://www.webofscience.com/wos/author/record/C-3855-2013",
    width: 300,
    height: 120,
  },
  {
    id: 2,
    name: "Scopus",
    image: "/research-profiles/elsevier-scopus.png",
    link: "https://www.scopus.com/authid/detail.uri?authorId=**********",
    width: 300,
    height: 120,
  },
  {
    id: 3,
    name: "ORCID",
    image: "/research-profiles/orcid.png",
    link: "https://orcid.org/0000-0002-0473-4920",
    width: 300,
    height: 120,
  },
  {
    id: 4,
    name: "ResearchGate",
    image: "/research-profiles/researchgate.png",
    link: "https://www.researchgate.net/profile/Nadeem-Butt",
    width: 300,
    height: 120,
  },
  {
    id: 5,
    name: "Google Scholar",
    image: "/research-profiles/google-scholar.png",
    link: "https://scholar.google.com/citations?user=q6DCadwAAAAJ&hl=en",
    width: 300,
    height: 120,
  },
  {
    id: 6,
    name: "Dimensions.ai",
    image: "/research-profiles/dimensions.png",
    link: "https://app.dimensions.ai/details/entities/publication/author/ur.01026256300.53",
    width: 300,
    height: 120,
  },
]

export default function ResearchPage() {
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  }

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const cardHover = {
    rest: { scale: 1 },
    hover: {
      scale: 1.03,
      boxShadow: "0 10px 30px rgba(0,0,0,0.1)",
      transition: {
        duration: 0.3,
        ease: "easeInOut",
      },
    },
  }

  return (
    <div>
      {/* Hero Section */}
      <motion.section
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        className="bg-gradient-to-r from-[#0a2158] to-[#1a3a7a] text-white py-16"
      >
        <div className="container mx-auto px-4 text-center">
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            className="text-3xl md:text-4xl font-bold mb-4"
          >
            Research
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
            className="text-xl max-w-3xl mx-auto"
          >
            Exploring statistical methodologies and their applications in medical and health sciences
          </motion.p>
        </div>
      </motion.section>

      {/* Research Profiles Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-bold mb-12 text-center"
          >
            <span className="text-[#4ecdc4]">Research Profiles</span>
            <motion.div
              initial={{ width: 0 }}
              whileInView={{ width: "96px" }}
              viewport={{ once: true }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="w-24 h-1 bg-[#f5a623] mx-auto mt-4"
            ></motion.div>
          </motion.h2>

          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {researchProfiles.map((profile) => (
              <motion.div key={profile.id} variants={fadeInUp}>
                <motion.div variants={cardHover} initial="rest" whileHover="hover" whileTap="hover">
                  <LinkWrapper href={profile.link} className="block">
                    <div className="bg-white rounded-lg shadow-lg p-6 h-40 flex items-center justify-center hover:shadow-xl transition-shadow duration-300 border border-gray-100">
                      <div className="relative h-full w-full">
                        <Image
                          src={profile.image || "/placeholder.svg"}
                          alt={profile.name}
                          fill
                          className="object-contain"
                        />
                      </div>
                    </div>
                  </LinkWrapper>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Publications Section - MOVED ABOVE RESEARCH AREAS */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-2xl font-bold mb-12 text-center"
          >
            Recent Publications
          </motion.h2>

          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 gap-6"
          >
            {publications.map((pub, index) => (
              <motion.div key={pub.id} variants={fadeInUp} custom={index}>
                <motion.div
                  variants={cardHover}
                  initial="rest"
                  whileHover="hover"
                  className="bg-white p-6 rounded-lg shadow-md"
                >
                  <h3 className="text-lg font-bold mb-2">
                    <LinkWrapper href={pub.link} className="hover:text-[#4ecdc4]">
                      {pub.title}
                    </LinkWrapper>
                  </h3>
                  <p className="text-gray-600 mb-2">{pub.authors}</p>
                  <p className="text-gray-700 mb-2">
                    <span className="font-semibold">{pub.journal}</span>, {pub.year}
                  </p>
                  <LinkWrapper href={pub.link} className="text-[#f5a623] hover:underline">
                    View Publication
                  </LinkWrapper>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4, duration: 0.6 }}
            className="text-center mt-12"
          >
            <LinkWrapper
              href="/publications"
              className="bg-[#0a2158] hover:bg-[#1a3a7a] text-white font-bold py-2 px-6 rounded transition duration-300"
            >
              View All Publications
            </LinkWrapper>
          </motion.div>
        </div>
      </section>

      {/* Research Areas Section - NOW BELOW PUBLICATIONS */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-2xl font-bold mb-12 text-center"
          >
            Research Areas
          </motion.h2>

          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {researchAreas.map((area, index) => (
              <motion.div key={area.id} variants={fadeInUp} custom={index}>
                <motion.div
                  variants={cardHover}
                  initial="rest"
                  whileHover="hover"
                  className="bg-white rounded-lg shadow-lg overflow-hidden h-full"
                >
                  <div className="bg-[#f5a623] p-4 flex justify-center">{area.icon}</div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold mb-3">{area.title}</h3>
                    <p className="text-gray-600">{area.description}</p>
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Research Projects Section */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-2xl font-bold mb-12 text-center"
          >
            Current Research Projects
          </motion.h2>

          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 gap-8"
          >
            {/* Project 1 */}
            <motion.div variants={fadeInUp}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white rounded-lg shadow-lg overflow-hidden"
              >
                <div className="relative h-48">
                  <Image src="/images/s1.jpg" alt="Research Project" fill className="object-cover" />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3">Statistical Methods for Analyzing COVID-19 Data</h3>
                  <p className="text-gray-600 mb-4">
                    Development and application of statistical methods for analyzing COVID-19 data to understand
                    patterns, trends, and factors affecting disease outcomes.
                  </p>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">2020 - Present</span>
                    <LinkWrapper href="#" className="text-[#f5a623] hover:underline">
                      Learn More
                    </LinkWrapper>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Project 2 */}
            <motion.div variants={fadeInUp}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white rounded-lg shadow-lg overflow-hidden"
              >
                <div className="relative h-48">
                  <Image src="/images/s2.jpg" alt="Research Project" fill className="object-cover" />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3">Machine Learning Applications in Medical Diagnosis</h3>
                  <p className="text-gray-600 mb-4">
                    Exploring the use of machine learning algorithms for improving medical diagnosis accuracy and
                    efficiency in various clinical settings.
                  </p>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">2021 - Present</span>
                    <LinkWrapper href="#" className="text-[#f5a623] hover:underline">
                      Learn More
                    </LinkWrapper>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Project 3 */}
            <motion.div variants={fadeInUp}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white rounded-lg shadow-lg overflow-hidden"
              >
                <div className="relative h-48">
                  <Image src="/images/s3.jpg" alt="Research Project" fill className="object-cover" />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3">Bayesian Methods in Clinical Trials</h3>
                  <p className="text-gray-600 mb-4">
                    Application of Bayesian statistical methods for designing and analyzing clinical trials to improve
                    efficiency and decision-making.
                  </p>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">2019 - Present</span>
                    <LinkWrapper href="#" className="text-[#f5a623] hover:underline">
                      Learn More
                    </LinkWrapper>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Project 4 */}
            <motion.div variants={fadeInUp}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white rounded-lg shadow-lg overflow-hidden"
              >
                <div className="relative h-48">
                  <Image src="/images/graph.png" alt="Research Project" fill className="object-cover" />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3">Statistical Methods for Handling Missing Data</h3>
                  <p className="text-gray-600 mb-4">
                    Development and evaluation of statistical methods for handling missing data in medical and health
                    research to improve data analysis validity.
                  </p>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">2020 - Present</span>
                    <LinkWrapper href="#" className="text-[#f5a623] hover:underline">
                      Learn More
                    </LinkWrapper>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Collaborations Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-2xl font-bold mb-12 text-center"
          >
            Research Collaborations
          </motion.h2>

          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {/* Collaboration 1 */}
            <motion.div variants={fadeInUp}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md text-center"
              >
                <div className="h-20 flex items-center justify-center mb-4">
                  <Image
                    src="/images/education.jpg"
                    alt="University Logo"
                    width={160}
                    height={80}
                    className="max-h-full"
                  />
                </div>
                <h3 className="text-lg font-bold mb-2">King Abdulaziz University</h3>
                <p className="text-gray-600">Saudi Arabia</p>
              </motion.div>
            </motion.div>

            {/* Collaboration 2 */}
            <motion.div variants={fadeInUp}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md text-center"
              >
                <div className="h-20 flex items-center justify-center mb-4">
                  <Image src="/images/s4.jpg" alt="University Logo" width={160} height={80} className="max-h-full" />
                </div>
                <h3 className="text-lg font-bold mb-2">Harvard Medical School</h3>
                <p className="text-gray-600">United States</p>
              </motion.div>
            </motion.div>

            {/* Collaboration 3 */}
            <motion.div variants={fadeInUp}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md text-center"
              >
                <div className="h-20 flex items-center justify-center mb-4">
                  <Image src="/images/s5.jpg" alt="University Logo" width={160} height={80} className="max-h-full" />
                </div>
                <h3 className="text-lg font-bold mb-2">University of Oxford</h3>
                <p className="text-gray-600">United Kingdom</p>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-[#0a2158] text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-2xl font-bold mb-6"
          >
            Interested in Research Collaboration?
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2, duration: 0.6 }}
            className="text-xl max-w-3xl mx-auto mb-8"
          >
            If you are interested in collaborating on research projects or have questions about our research work,
            please get in touch.
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            <LinkWrapper
              href="/contact"
              className="bg-[#f5a623] hover:bg-[#e09612] text-white font-bold py-3 px-8 rounded-full transition duration-300"
            >
              Contact for Collaboration
            </LinkWrapper>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
