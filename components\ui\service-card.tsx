"use client"

import React from "react"
import { LucideIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"

interface ServiceCardProps {
  icon: LucideIcon
  title: string
  description: string
  features?: string[]
  ctaText?: string
  ctaHref?: string
  className?: string
  iconColor?: string
  gradient?: string
  delay?: number
}

export function ServiceCard({
  icon: Icon,
  title,
  description,
  features,
  ctaText,
  ctaHref,
  className,
  iconColor = "bg-accent",
  gradient = "from-accent/10 to-transparent",
  delay = 0,
}: ServiceCardProps) {
  const cardId = `service-${title.toLowerCase().replace(/\s+/g, '-')}`

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      if (ctaHref) {
        window.location.href = ctaHref
      }
    }
  }

  return (
    <Card
      className={cn(
        "group relative overflow-hidden border-0 bg-white shadow-lg transition-all duration-500 ease-out",
        "hover:shadow-2xl hover:shadow-accent/10 hover:-translate-y-2",
        "focus:shadow-2xl focus:shadow-accent/10 focus:-translate-y-2",
        "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-white",
        "before:absolute before:inset-0 before:bg-gradient-to-br before:from-transparent before:via-transparent before:to-accent/5",
        "before:opacity-0 before:transition-opacity before:duration-500 hover:before:opacity-100 focus:before:opacity-100",
        "animate-card-entrance service-card",
        className
      )}
      style={{
        animationDelay: `${delay}ms`,
      }}
      role="article"
      tabIndex={0}
      aria-labelledby={`${cardId}-title`}
      aria-describedby={`${cardId}-description`}
      onKeyDown={handleKeyDown}
    >
      {/* Gradient overlay */}
      <div 
        className={cn(
          "absolute inset-0 bg-gradient-to-br opacity-0 transition-opacity duration-500 group-hover:opacity-100",
          gradient
        )}
      />
      
      <CardHeader className="relative z-10 pb-4 px-4 sm:px-6">
        {/* Icon container with enhanced styling */}
        <div className="flex justify-center mb-4 sm:mb-6">
          <div
            className={cn(
              "relative p-3 sm:p-4 rounded-2xl shadow-lg transition-all duration-500",
              "group-hover:scale-110 group-hover:rotate-3 group-hover:shadow-xl",
              "group-focus:scale-110 group-focus:rotate-3 group-focus:shadow-xl",
              iconColor
            )}
          >
            {/* Icon glow effect */}
            <div className="absolute inset-0 rounded-2xl bg-white/20 opacity-0 transition-opacity duration-500 group-hover:opacity-100 group-focus:opacity-100" />
            <Icon
              className="h-7 w-7 sm:h-8 sm:w-8 text-white relative z-10"
              aria-hidden="true"
            />
          </div>
        </div>

        <CardTitle
          id={`${cardId}-title`}
          className="text-xl sm:text-2xl font-bold text-center mb-3 text-gray-900 group-hover:text-primary group-focus:text-primary transition-colors duration-300"
        >
          {title}
        </CardTitle>
      </CardHeader>

      <CardContent className="relative z-10 px-4 sm:px-6 pb-4">
        <CardDescription
          id={`${cardId}-description`}
          className="text-sm sm:text-base text-gray-600 text-center leading-relaxed mb-4 group-hover:text-gray-700 group-focus:text-gray-700 transition-colors duration-300"
        >
          {description}
        </CardDescription>
        
        {/* Features list */}
        {features && features.length > 0 && (
          <ul
            className="space-y-2 mb-4"
            role="list"
            aria-label={`${title} features`}
          >
            {features.map((feature, index) => (
              <li
                key={index}
                className="flex items-start text-sm text-gray-600 group-hover:text-gray-700 group-focus:text-gray-700 transition-colors duration-300"
              >
                <div
                  className="w-1.5 h-1.5 rounded-full bg-accent mt-2 mr-3 flex-shrink-0"
                  aria-hidden="true"
                />
                <span>{feature}</span>
              </li>
            ))}
          </ul>
        )}
      </CardContent>

      {/* Call-to-action footer */}
      {ctaText && (
        <CardFooter className="relative z-10 pt-2 pb-4 sm:pb-6 px-4 sm:px-6">
          <Button
            asChild={!!ctaHref}
            variant="outline"
            size="sm"
            className={cn(
              "w-full group-hover:bg-primary group-hover:text-white group-hover:border-primary",
              "group-focus:bg-primary group-focus:text-white group-focus:border-primary",
              "transition-all duration-300 hover:scale-105 focus:scale-105",
              "border-2 border-gray-200 text-gray-700 font-medium text-sm sm:text-base",
              "py-2 sm:py-3"
            )}
            aria-label={`Learn more about ${title}`}
          >
            {ctaHref ? (
              <a href={ctaHref} className="w-full text-center">
                {ctaText}
              </a>
            ) : (
              <span>{ctaText}</span>
            )}
          </Button>
        </CardFooter>
      )}

      {/* Decorative elements */}
      <div className="absolute top-4 right-4 w-20 h-20 bg-gradient-to-br from-accent/10 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      <div className="absolute bottom-4 left-4 w-12 h-12 bg-gradient-to-br from-secondary/10 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700" />
    </Card>
  )
}

// Enhanced services section wrapper
interface ServicesGridProps {
  children: React.ReactNode
  title?: string
  subtitle?: string
  className?: string
}

export function ServicesGrid({
  children,
  title = "Our Services",
  subtitle = "Comprehensive statistical solutions tailored to your needs",
  className
}: ServicesGridProps) {
  return (
    <section className={cn("py-12 sm:py-16 lg:py-20 bg-gradient-to-b from-gray-50 to-white", className)}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6">
            {title}
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-2xl lg:max-w-3xl mx-auto leading-relaxed px-4 sm:px-0">
            {subtitle}
          </p>
          {/* Decorative line */}
          <div className="w-16 sm:w-24 h-1 bg-gradient-to-r from-accent to-secondary mx-auto mt-6 sm:mt-8 rounded-full" />
        </div>

        {/* Services grid with responsive breakpoints */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10 max-w-7xl mx-auto">
          {children}
        </div>
      </div>
    </section>
  )
}
