"use client"

import type React from "react"

import { useState, useRef } from "react"
import {
  <PERSON><PERSON><PERSON>,
  Line<PERSON>hart,
  Save,
  Plus,
  Upload,
  Search,
  Settings,
  Download,
  ChevronLeft,
  ChevronRight,
  HelpCircle,
  Table,
  BarChart4,
} from "lucide-react"
import Papa from "papaparse"
import * as XLSX from "xlsx"

// Import types and registries
import type {
  DataSet,
  SortConfig,
  FilterConfig,
  RecodeConfig,
  TransformConfig,
  AnalysisType,
  ChartType,
  AnalysisResult,
  ChartResult,
  DataRow,
} from "./statistical-analysis/types"
import { getAnalysisByType } from "./statistical-analysis/analysis-registry"
import { getChartByType } from "./statistical-analysis/chart-registry"

// Import components
import DataTable from "./statistical-analysis/data-table"
import AnalysisModal from "./statistical-analysis/analysis-modal"
import ChartModal from "./statistical-analysis/chart-modal"
import AnalysisResultDisplay from "./statistical-analysis/analysis-result"
import ChartResultDisplay from "./statistical-analysis/chart-result"

export default function StatisticalAnalysisApp() {
  // State
  const [dataset, setDataset] = useState<DataSet | null>(null)
  const [results, setResults] = useState<(AnalysisResult | ChartResult)[]>([])
  const [showAnalysisModal, setShowAnalysisModal] = useState(false)
  const [showChartModal, setShowChartModal] = useState(false)
  const [showDataManagementModal, setShowDataManagementModal] = useState(false)
  const [dataManagementTab, setDataManagementTab] = useState<"sort" | "filter" | "recode" | "transform">("sort")

  // Layout state
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [activePanel, setActivePanel] = useState<"data" | "results">("data")
  // Data management state
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null)
  const [filterConfigs, setFilterConfigs] = useState<FilterConfig[]>([])
  const [recodeConfig, setRecodeConfig] = useState<RecodeConfig>({ column: "", oldValue: "", newValue: "" })
  const [transformConfig, setTransformConfig] = useState<TransformConfig>({ column: "", operation: "log" })
  const [searchTerm, setSearchTerm] = useState("")

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Detect column types (numeric or categorical)
  const detectColumnTypes = (data: DataRow[]): Record<string, "numeric" | "categorical"> => {
    if (!data.length) return {}

    const columnTypes: Record<string, "numeric" | "categorical"> = {}
    const headers = Object.keys(data[0])

    headers.forEach((header) => {
      // Check first 10 rows (or all if less than 10)
      const sampleSize = Math.min(10, data.length)
      let numericCount = 0

      for (let i = 0; i < sampleSize; i++) {
        const value = data[i][header]
        if (value === null || value === undefined || value === "") continue

        // Check if value can be converted to a number
        const numValue = Number(value)
        if (!isNaN(numValue)) {
          numericCount++
        }
      }

      // If more than 70% of sample values are numeric, consider it a numeric column
      columnTypes[header] = numericCount / sampleSize > 0.7 ? "numeric" : "categorical"
    })

    return columnTypes
  }

  // Handle file import
  const handleImport = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Check if file is CSV or Excel
    if (file.name.endsWith(".csv")) {
      Papa.parse(file, {
        header: true,
        complete: (results) => {
          const headers = results.meta.fields || []
          const rows = results.data as DataRow[]

          // Detect column types
          const columnTypes = detectColumnTypes(rows)

          setDataset({
            headers,
            rows,
            originalRows: [...rows],
            columnTypes,
          })
        },
        error: (error) => {
          console.error("Error parsing CSV:", error)
          alert("Error parsing CSV file")
        },
      })
    } else if (file.name.endsWith(".xlsx") || file.name.endsWith(".xls")) {
      // Use xlsx library to parse Excel files
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const data = e.target?.result
          if (!data) return

          const workbook = XLSX.read(data, { type: "binary" })
          const sheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[sheetName]

          // Convert to JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet)

          // Extract headers
          const headers = Object.keys(jsonData[0] || {})

          // Detect column types
          const columnTypes = detectColumnTypes(jsonData as DataRow[])

          setDataset({
            headers,
            rows: jsonData as DataRow[],
            originalRows: [...(jsonData as DataRow[])],
            columnTypes,
          })
        } catch (error) {
          console.error("Error parsing Excel file:", error)
          alert("Error parsing Excel file")
        }
      }
      reader.onerror = () => {
        alert("Error reading Excel file")
      }
      reader.readAsBinaryString(file)
    } else {
      alert("Please upload a CSV or Excel file")
    }

    // Reset file input
    if (e.target) {
      e.target.value = ""
    }
  }

  // Handle sort
  const handleSort = (column: string, direction: "asc" | "desc") => {
    if (!dataset) return

    const sortedRows = [...dataset.rows].sort((a, b) => {
      const valueA = a[column]
      const valueB = b[column]

      if (typeof valueA === "number" && typeof valueB === "number") {
        return direction === "asc" ? valueA - valueB : valueB - valueA
      }

      const strA = String(valueA).toLowerCase()
      const strB = String(valueB).toLowerCase()

      if (strA < strB) return direction === "asc" ? -1 : 1
      if (strA > strB) return direction === "asc" ? 1 : -1
      return 0
    })

    setDataset({
      ...dataset,
      rows: sortedRows,
    })

    setSortConfig({ column, direction })
  }

  // Handle new project
  const handleNewProject = () => {
    if (confirm("Are you sure you want to start a new project? All current data and results will be lost.")) {
      setDataset(null)
      setResults([])
      setSortConfig(null)
      setFilterConfigs([])
      setSearchTerm("")
    }
  }

  // Handle save project
  const handleSaveProject = () => {
    alert("In a full implementation, this would save your project data and results.")
  }

  // Handle export data
  const handleExportData = () => {
    if (!dataset) return

    // Create a workbook and add the data
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.json_to_sheet(dataset.rows)
    XLSX.utils.book_append_sheet(wb, ws, "Data")

    // Save the file
    XLSX.writeFile(wb, "statistical_analysis_data.xlsx")
  }

  // Handle run analysis
  const handleRunAnalysis = (type: AnalysisType, variables: string[], groupingVariable?: string) => {
    if (!dataset) return

    const analysis = getAnalysisByType(type)
    if (!analysis) return

    const result = analysis.execute(dataset, variables, groupingVariable)
    setResults([...results, result])

    // Automatically show results panel when analysis is run
    setActivePanel("results")
  }

  // Handle create chart
  const handleCreateChart = (type: ChartType, variables: string[]) => {
    if (!dataset) return

    const chart = getChartByType(type)
    if (!chart) return

    const result = chart.execute(dataset, variables)
    setResults([...results, result])

    // Automatically show results panel when chart is created
    setActivePanel("results")
  }

  // Handle remove result
  const handleRemoveResult = (id: string) => {
    setResults(results.filter((result) => "id" in result && result.id !== id))
  }

  // Toggle sidebar
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  return (
    <div className="flex flex-col h-[800px] bg-gray-50">
      {/* Header */}
      <div className="bg-[#2196f3] text-white p-4 flex items-center justify-between">
        <div className="flex items-center">
          <div className="mr-4">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect width="10" height="10" fill="#FF5722" />
              <rect x="11" width="10" height="10" fill="#4CAF50" />
              <rect x="22" width="10" height="10" fill="#2196F3" />
              <rect y="11" width="10" height="10" fill="#FFC107" />
              <rect x="11" y="11" width="10" height="10" fill="#9C27B0" />
              <rect x="22" y="11" width="10" height="10" fill="#F44336" />
              <rect y="22" width="10" height="10" fill="#3F51B5" />
              <rect x="11" y="22" width="10" height="10" fill="#00BCD4" />
              <rect x="22" y="22" width="10" height="10" fill="#E91E63" />
            </svg>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handleNewProject}
              className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded flex items-center transition-colors"
            >
              <Plus className="h-4 w-4 mr-2" />
              New project
            </button>
            <button
              onClick={handleSaveProject}
              className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded flex items-center transition-colors"
            >
              <Save className="h-4 w-4 mr-2" />
              Save Project
            </button>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={handleExportData}
            className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded flex items-center transition-colors"
            disabled={!dataset}
          >
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </button>
          <button
            className="bg-white/20 hover:bg-white/30 text-white p-2 rounded flex items-center transition-colors"
            title="Help"
          >
            <HelpCircle className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <div
          className={`bg-white border-r flex flex-col ${sidebarCollapsed ? "w-16" : "w-64"} transition-all duration-300`}
        >
          <div className="p-4 border-b">
            <h3 className={`font-medium ${sidebarCollapsed ? "hidden" : "block"}`}>Tools</h3>
            <button
              onClick={toggleSidebar}
              className={`${sidebarCollapsed ? "mx-auto" : "hidden"} text-gray-500 hover:text-gray-700`}
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          </div>

          <div className="flex-1 overflow-y-auto p-2">
            <div className={`mb-4 ${sidebarCollapsed ? "text-center" : ""}`}>
              <button
                onClick={handleImport}
                className={`w-full bg-gray-100 hover:bg-gray-200 text-gray-700 rounded transition-colors ${
                  sidebarCollapsed ? "p-3" : "p-3 text-left"
                }`}
                title="Import Data"
              >
                <Upload className={`h-5 w-5 ${sidebarCollapsed ? "mx-auto" : "inline mr-2"}`} />
                {!sidebarCollapsed && <span>Import Data</span>}
              </button>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                accept=".csv,.xlsx,.xls"
                className="hidden"
              />
            </div>

            <div className={`mb-4 ${sidebarCollapsed ? "text-center" : ""}`}>
              <button
                onClick={() => setShowAnalysisModal(true)}
                disabled={!dataset}
                className={`w-full bg-gray-100 hover:bg-gray-200 text-gray-700 rounded transition-colors ${
                  sidebarCollapsed ? "p-3" : "p-3 text-left"
                } disabled:opacity-50 disabled:cursor-not-allowed`}
                title="Run Analysis"
              >
                <LineChart className={`h-5 w-5 ${sidebarCollapsed ? "mx-auto" : "inline mr-2"}`} />
                {!sidebarCollapsed && <span>Run Analysis</span>}
              </button>
            </div>

            <div className={`mb-4 ${sidebarCollapsed ? "text-center" : ""}`}>
              <button
                onClick={() => setShowChartModal(true)}
                disabled={!dataset}
                className={`w-full bg-gray-100 hover:bg-gray-200 text-gray-700 rounded transition-colors ${
                  sidebarCollapsed ? "p-3" : "p-3 text-left"
                } disabled:opacity-50 disabled:cursor-not-allowed`}
                title="Create Chart"
              >
                <BarChart className={`h-5 w-5 ${sidebarCollapsed ? "mx-auto" : "inline mr-2"}`} />
                {!sidebarCollapsed && <span>Create Chart</span>}
              </button>
            </div>

            <div className={`mb-4 ${sidebarCollapsed ? "text-center" : ""}`}>
              <button
                onClick={() => setShowDataManagementModal(true)}
                disabled={!dataset}
                className={`w-full bg-gray-100 hover:bg-gray-200 text-gray-700 rounded transition-colors ${
                  sidebarCollapsed ? "p-3" : "p-3 text-left"
                } disabled:opacity-50 disabled:cursor-not-allowed`}
                title="Manage Data"
              >
                <Settings className={`h-5 w-5 ${sidebarCollapsed ? "mx-auto" : "inline mr-2"}`} />
                {!sidebarCollapsed && <span>Manage Data</span>}
              </button>
            </div>
          </div>

          <div className="p-4 border-t">
            {!sidebarCollapsed && (
              <button onClick={toggleSidebar} className="text-gray-500 hover:text-gray-700" title="Collapse Sidebar">
                <ChevronLeft className="h-5 w-5" />
              </button>
            )}
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 flex flex-col">
          {/* Tab Navigation */}
          <div className="bg-white border-b">
            <div className="flex">
              <button
                onClick={() => setActivePanel("data")}
                className={`py-3 px-6 flex items-center ${
                  activePanel === "data"
                    ? "border-b-2 border-blue-500 text-blue-600"
                    : "text-gray-600 hover:text-gray-800"
                }`}
              >
                <Table className="h-4 w-4 mr-2" />
                Data
                {dataset && (
                  <span className="ml-2 text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full">
                    {dataset.rows.length}
                  </span>
                )}
              </button>
              <button
                onClick={() => setActivePanel("results")}
                className={`py-3 px-6 flex items-center ${
                  activePanel === "results"
                    ? "border-b-2 border-blue-500 text-blue-600"
                    : "text-gray-600 hover:text-gray-800"
                }`}
              >
                <BarChart4 className="h-4 w-4 mr-2" />
                Results
                {results.length > 0 && (
                  <span className="ml-2 text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full">
                    {results.length}
                  </span>
                )}
              </button>
            </div>
          </div>

          {/* Content Panels */}
          <div className="flex-1 overflow-hidden">
            {/* Tabbed View */}
            <div className="h-full">
              {/* Data Panel */}
              {activePanel === "data" && (
                <div className="h-full flex flex-col">
                  <div className="bg-white p-4 flex justify-between items-center border-b border-gray-200">
                    <div className="flex items-center">
                      <span className="font-medium">Data</span>
                      {dataset && (
                        <span className="ml-2 text-sm text-gray-500">
                          ({dataset.rows.length} rows, {dataset.headers.length} columns)
                        </span>
                      )}
                    </div>

                    {dataset && (
                      <div className="flex items-center">
                        <div className="relative">
                          <input
                            type="text"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            placeholder="Search..."
                            className="w-40 px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                          />
                          <button
                            onClick={() => {
                              // Implement search functionality
                            }}
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          >
                            <Search className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex-1 overflow-auto p-4">
                    {dataset ? (
                      <div className="h-full">
                        <DataTable dataset={dataset} sortConfig={sortConfig} onSort={handleSort} />
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center h-full text-gray-400">
                        <Upload className="h-16 w-16 mb-4" />
                        <p className="text-center">No data loaded. Import a CSV or Excel file to begin.</p>
                        <button
                          onClick={handleImport}
                          className="mt-4 bg-[#2196f3] hover:bg-[#1976d2] text-white px-4 py-2 rounded transition-colors"
                        >
                          Import Data
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Results Panel */}
              {activePanel === "results" && (
                <div className="h-full flex flex-col">
                  <div className="bg-white p-4 flex justify-between items-center border-b border-gray-200">
                    <div className="flex items-center">
                      <span className="font-medium">Results</span>
                      {results.length > 0 && (
                        <span className="ml-2 text-sm text-gray-500">({results.length} items)</span>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setShowAnalysisModal(true)}
                        disabled={!dataset}
                        className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Analysis
                      </button>
                      <button
                        onClick={() => setShowChartModal(true)}
                        disabled={!dataset}
                        className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Chart
                      </button>
                    </div>
                  </div>

                  <div className="flex-1 overflow-auto p-4">
                    {results.length > 0 ? (
                      <div>
                        {results.map((result) => {
                          if ("type" in result) {
                            // It's a chart result
                            return (
                              <ChartResultDisplay
                                key={result.id}
                                result={result}
                                dataset={dataset!}
                                onRemove={handleRemoveResult}
                              />
                            )
                          } else {
                            // It's an analysis result
                            return (
                              <AnalysisResultDisplay key={result.id} result={result} onRemove={handleRemoveResult} />
                            )
                          }
                        })}
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center h-full text-gray-400">
                        <LineChart className="h-16 w-16 mb-4" />
                        <p className="text-center">
                          No analyses or charts yet. Run an analysis or create a chart to see results here.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      {dataset && (
        <>
          <AnalysisModal
            isOpen={showAnalysisModal}
            onClose={() => setShowAnalysisModal(false)}
            onRunAnalysis={handleRunAnalysis}
            dataset={dataset}
          />

          <ChartModal
            isOpen={showChartModal}
            onClose={() => setShowChartModal(false)}
            onCreateChart={handleCreateChart}
            dataset={dataset}
          />

          {/* Data Management Modal would go here */}
        </>
      )}
    </div>
  )
}
