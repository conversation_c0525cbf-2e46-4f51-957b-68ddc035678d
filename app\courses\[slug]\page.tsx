import Image from "next/image"
import Link from "next/link"
import { ArrowLeft, Book, FileText, Database, BookOpen, CheckCircle } from "lucide-react"
import { notFound } from "next/navigation"

// Define the course data structure
type CourseSection = {
  title: string
  content: string[]
}

type Course = {
  id: number
  slug: string
  title: string
  image: string
  description: string
  introduction: string
  content: CourseSection[]
  assignments: string[]
  caseStudies: string[]
  datasets: { name: string; description: string }[]
  textbooks: { title: string; author: string; link?: string }[]
}

// Course data
const courses: Course[] = [
  {
    id: 1,
    slug: "biostatistics",
    title: "Biostatistics",
    image: "/biostatistics.jpeg",
    description:
      "Introduction to Biostatistics course provides an introduction to selected important and commonly used topics in medical research.",
    introduction:
      "Biostatistics is the application of statistical methods to biological data and is essential for medical research. This course introduces fundamental concepts in biostatistics, including study design, data collection, analysis, and interpretation. Students will learn how to apply statistical methods to real-world medical and biological problems.",
    content: [
      {
        title: "Module 1: Introduction to Biostatistics",
        content: [
          "Definition and scope of biostatistics",
          "Role of biostatistics in medical research",
          "Types of data in biomedical studies",
          "Scales of measurement",
          "Basic statistical concepts and terminology",
        ],
      },
      {
        title: "Module 2: Descriptive Statistics",
        content: [
          "Measures of central tendency",
          "Measures of dispersion",
          "Data visualization techniques",
          "Graphical representation of medical data",
          "Interpreting descriptive statistics in medical context",
        ],
      },
      {
        title: "Module 3: Probability and Distributions",
        content: [
          "Basic probability concepts",
          "Probability distributions (Normal, Binomial, Poisson)",
          "Sampling distributions",
          "Central Limit Theorem",
          "Applications in medical research",
        ],
      },
      {
        title: "Module 4: Statistical Inference",
        content: [
          "Hypothesis testing",
          "Type I and Type II errors",
          "P-values and confidence intervals",
          "Sample size determination",
          "Power analysis for medical studies",
        ],
      },
      {
        title: "Module 5: Comparative Statistics",
        content: [
          "t-tests (one-sample, two-sample, paired)",
          "Analysis of Variance (ANOVA)",
          "Non-parametric tests",
          "Multiple comparison procedures",
          "Interpreting results in clinical context",
        ],
      },
    ],
    assignments: [
      "Statistical analysis of a public health dataset",
      "Design and analysis of a simulated clinical trial",
      "Critical evaluation of statistical methods in published medical papers",
      "Sample size calculation for different study designs",
      "Data visualization project using real medical data",
    ],
    caseStudies: [
      "Analysis of COVID-19 epidemiological data",
      "Statistical evaluation of a new treatment method",
      "Risk factor analysis for cardiovascular disease",
      "Survival analysis in cancer research",
      "Bioequivalence studies in pharmaceutical research",
    ],
    datasets: [
      {
        name: "Framingham Heart Study Dataset",
        description: "Longitudinal data for cardiovascular disease risk factors",
      },
      {
        name: "Cancer Survival Dataset",
        description: "Patient data with survival times and treatment information",
      },
      {
        name: "Clinical Trial Simulator",
        description: "Tool for generating simulated clinical trial data",
      },
      {
        name: "WHO Global Health Observatory",
        description: "International health statistics and indicators",
      },
    ],
    textbooks: [
      {
        title: "Fundamentals of Biostatistics",
        author: "Bernard Rosner",
        link: "https://www.amazon.com/Fundamentals-Biostatistics-Bernard-Rosner/dp/**********",
      },
      {
        title: "Principles of Biostatistics",
        author: "Marcello Pagano and Kimberlee Gauvreau",
        link: "https://www.amazon.com/Principles-Biostatistics-Marcello-Pagano/dp/**********",
      },
      {
        title: "Medical Statistics at a Glance",
        author: "Aviva Petrie and Caroline Sabin",
        link: "https://www.amazon.com/Medical-Statistics-Glance-Aviva-Petrie/dp/**********",
      },
    ],
  },
  {
    id: 2,
    slug: "survival-analysis",
    title: "Survival Analysis",
    image: "/statistical-vs-simulations.png",
    description:
      "The main idea of the course is to develop a critical approach to the analysis of survival data often encountered in health and actuarial sciences research",
    introduction:
      "Survival Analysis is a branch of statistics focused on analyzing the expected duration of time until an event of interest occurs. This course provides a comprehensive introduction to survival analysis methods commonly used in medical research, clinical trials, and epidemiological studies. Students will learn both theoretical foundations and practical applications using real-world data.",
    content: [
      {
        title: "Module 1: Introduction to Survival Analysis",
        content: [
          "Basic concepts and terminology",
          "Censoring and truncation",
          "Survival, hazard, and cumulative hazard functions",
          "Applications in medical and health research",
          "Types of survival studies",
        ],
      },
      {
        title: "Module 2: Non-parametric Methods",
        content: [
          "Life tables",
          "Kaplan-Meier estimator",
          "Nelson-Aalen estimator",
          "Log-rank test and other comparison tests",
          "Graphical methods for survival data",
        ],
      },
      {
        title: "Module 3: Semi-parametric Methods",
        content: [
          "Cox proportional hazards model",
          "Model building and variable selection",
          "Checking proportional hazards assumption",
          "Time-dependent covariates",
          "Stratified Cox models",
        ],
      },
      {
        title: "Module 4: Parametric Models",
        content: [
          "Exponential, Weibull, and log-logistic models",
          "Accelerated failure time models",
          "Model selection and goodness-of-fit",
          "Frailty models",
          "Competing risks analysis",
        ],
      },
      {
        title: "Module 5: Advanced Topics",
        content: [
          "Recurrent event analysis",
          "Joint modeling of longitudinal and survival data",
          "Bayesian approaches to survival analysis",
          "Machine learning methods for survival prediction",
          "Current research trends in survival analysis",
        ],
      },
    ],
    assignments: [
      "Analysis of a clinical trial dataset using Kaplan-Meier and log-rank tests",
      "Building and interpreting Cox proportional hazards models",
      "Comparison of parametric and semi-parametric approaches",
      "Survival analysis project using real medical data",
      "Critical review of survival analysis methods in published literature",
    ],
    caseStudies: [
      "Cancer survival analysis with multiple treatment arms",
      "Time-to-event analysis in cardiovascular studies",
      "Recurrent hospitalization analysis in chronic disease patients",
      "Competing risks analysis in transplant studies",
      "Survival prediction models in critical care medicine",
    ],
    datasets: [
      {
        name: "Veterans' Administration Lung Cancer Trial",
        description: "Classic dataset for survival analysis demonstrations",
      },
      {
        name: "Stanford Heart Transplant Data",
        description: "Survival times of heart transplant patients",
      },
      {
        name: "SEER Cancer Statistics",
        description: "Comprehensive cancer incidence and survival data",
      },
      {
        name: "Framingham Heart Study",
        description: "Longitudinal data for cardiovascular event analysis",
      },
    ],
    textbooks: [
      {
        title: "Survival Analysis: Techniques for Censored and Truncated Data",
        author: "John P. Klein and Melvin L. Moeschberger",
        link: "https://www.amazon.com/Survival-Analysis-Techniques-Truncated-Statistics/dp/**********",
      },
      {
        title: "Applied Survival Analysis: Regression Modeling of Time-to-Event Data",
        author: "David W. Hosmer, Stanley Lemeshow, and Susanne May",
        link: "https://www.amazon.com/Applied-Survival-Analysis-Time-Event/dp/**********",
      },
      {
        title: "Modelling Survival Data in Medical Research",
        author: "David Collett",
        link: "https://www.amazon.com/Modelling-Survival-Medical-Research-Chapman/dp/**********",
      },
    ],
  },
  {
    id: 3,
    slug: "quantitative-techniques",
    title: "Quantitative Techniques",
    image: "/quantitative-techniques.png",
    description:
      "It is applied course in statistics that is designed to provide you with the concepts and methods of statistical analysis for decision making under uncertainties",
    introduction:
      "Quantitative Techniques is an applied course that equips students with statistical methods and mathematical tools for decision-making in business and research environments. This course focuses on practical applications of quantitative methods to solve real-world problems, analyze data, and make informed decisions under uncertainty.",
    content: [
      {
        title: "Module 1: Introduction to Quantitative Methods",
        content: [
          "Role of quantitative techniques in decision making",
          "Types of business and research problems",
          "Data collection methods and sources",
          "Measurement scales and data types",
          "Introduction to statistical software packages",
        ],
      },
      {
        title: "Module 2: Descriptive Statistics and Data Visualization",
        content: [
          "Measures of central tendency and dispersion",
          "Data distribution and normality",
          "Graphical representation of data",
          "Dashboard creation and interpretation",
          "Exploratory data analysis techniques",
        ],
      },
      {
        title: "Module 3: Probability and Decision Theory",
        content: [
          "Probability concepts and applications",
          "Decision trees and expected value",
          "Bayesian decision making",
          "Risk analysis and utility theory",
          "Monte Carlo simulation methods",
        ],
      },
      {
        title: "Module 4: Statistical Inference and Hypothesis Testing",
        content: [
          "Sampling methods and distributions",
          "Confidence intervals and estimation",
          "Hypothesis testing for business decisions",
          "ANOVA and MANOVA",
          "Non-parametric tests for business data",
        ],
      },
      {
        title: "Module 5: Predictive Modeling and Forecasting",
        content: [
          "Correlation and regression analysis",
          "Multiple regression models",
          "Time series analysis and forecasting",
          "Trend analysis and seasonal adjustments",
          "Introduction to machine learning applications",
        ],
      },
    ],
    assignments: [
      "Market research data analysis project",
      "Sales forecasting using time series methods",
      "Decision analysis case using decision trees",
      "Regression modeling for business prediction",
      "Dashboard creation for business metrics",
    ],
    caseStudies: [
      "Inventory optimization for retail business",
      "Customer segmentation using cluster analysis",
      "Demand forecasting for manufacturing",
      "Quality control implementation using statistical methods",
      "Risk assessment for financial investments",
    ],
    datasets: [
      {
        name: "Retail Sales Dataset",
        description: "Historical sales data for retail forecasting exercises",
      },
      {
        name: "Customer Satisfaction Survey Data",
        description: "Survey responses for service quality analysis",
      },
      {
        name: "Stock Market Historical Data",
        description: "Financial time series for investment analysis",
      },
      {
        name: "Manufacturing Quality Control Data",
        description: "Production metrics and defect rates for quality analysis",
      },
    ],
    textbooks: [
      {
        title: "Quantitative Methods for Business",
        author: "David R. Anderson, Dennis J. Sweeney, and Thomas A. Williams",
        link: "https://www.amazon.com/Quantitative-Methods-Business-David-Anderson/dp/1111526915",
      },
      {
        title: "Business Statistics: A Decision-Making Approach",
        author: "David F. Groebner, Patrick W. Shannon, and Phillip C. Fry",
        link: "https://www.amazon.com/Business-Statistics-Decision-Making-Approach/dp/0134496493",
      },
      {
        title: "Statistics for Business: Decision Making and Analysis",
        author: "Robert Stine and Dean Foster",
        link: "https://www.amazon.com/Statistics-Business-Decision-Making-Analysis/dp/0134497163",
      },
    ],
  },
  {
    id: 4,
    slug: "statistical-computing",
    title: "Statistical Computing",
    image: "/r-logo.png",
    description:
      "Learn modern statistical computing techniques using R, Python, and other statistical software for data analysis and visualization.",
    introduction:
      "Statistical Computing introduces students to computational methods and software tools essential for modern data analysis. This course focuses on practical implementation of statistical techniques using R, Python, and other specialized software. Students will develop programming skills necessary for data manipulation, visualization, statistical modeling, and reproducible research.",
    content: [
      {
        title: "Module 1: Introduction to Statistical Computing",
        content: [
          "Overview of statistical software packages",
          "Introduction to R programming environment",
          "Basic Python for data analysis",
          "Data structures and data types",
          "Script writing and reproducible research practices",
        ],
      },
      {
        title: "Module 2: Data Management and Preprocessing",
        content: [
          "Data import and export in various formats",
          "Data cleaning and validation techniques",
          "Data transformation and feature engineering",
          "Handling missing data",
          "Data merging and reshaping",
        ],
      },
      {
        title: "Module 3: Data Visualization",
        content: [
          "Principles of effective data visualization",
          "Creating static visualizations with ggplot2 (R) and Matplotlib/Seaborn (Python)",
          "Interactive visualizations with Plotly and Shiny",
          "Dashboard creation",
          "Visualization for different data types and relationships",
        ],
      },
      {
        title: "Module 4: Statistical Modeling Implementation",
        content: [
          "Linear and generalized linear models",
          "Regression diagnostics and model validation",
          "Resampling methods: bootstrap and cross-validation",
          "Regularization techniques",
          "Model selection and evaluation",
        ],
      },
      {
        title: "Module 5: Advanced Topics and Applications",
        content: [
          "Machine learning implementation in R and Python",
          "Time series analysis and forecasting",
          "Spatial data analysis",
          "Text mining and natural language processing basics",
          "Big data considerations and parallel computing",
        ],
      },
    ],
    assignments: [
      "Data cleaning and preprocessing project",
      "Creating a comprehensive data visualization portfolio",
      "Statistical modeling and interpretation report",
      "Building an interactive dashboard with Shiny or Dash",
      "Reproducible research project with R Markdown or Jupyter Notebooks",
    ],
    caseStudies: [
      "Predictive modeling for healthcare outcomes",
      "Market basket analysis for retail data",
      "Sentiment analysis of customer reviews",
      "Time series forecasting for financial data",
      "Spatial analysis of epidemiological data",
    ],
    datasets: [
      {
        name: "UCI Machine Learning Repository",
        description: "Collection of datasets for various analytical tasks",
      },
      {
        name: "Kaggle Datasets",
        description: "Real-world datasets with analytical challenges",
      },
      {
        name: "World Health Organization Data",
        description: "Global health statistics for analysis",
      },
      {
        name: "Financial Markets Data",
        description: "Stock market and economic indicators",
      },
    ],
    textbooks: [
      {
        title: "R for Data Science",
        author: "Hadley Wickham and Garrett Grolemund",
        link: "https://r4ds.had.co.nz/",
      },
      {
        title: "Python for Data Analysis",
        author: "Wes McKinney",
        link: "https://www.amazon.com/Python-Data-Analysis-Wrangling-IPython/dp/**********",
      },
      {
        title: "Statistical Computing with R",
        author: "Maria L. Rizzo",
        link: "https://www.amazon.com/Statistical-Computing-Chapman-Hall-Series/dp/**********",
      },
      {
        title: "An Introduction to Statistical Learning",
        author: "Gareth James, Daniela Witten, Trevor Hastie, and Robert Tibshirani",
        link: "https://www.statlearning.com/",
      },
    ],
  },
  {
    id: 5,
    slug: "research-methodology",
    title: "Research Methodology",
    image: "/categorical-data-analysis.gif",
    description:
      "Comprehensive course on research design, data collection methods, and analysis techniques for conducting high-quality research.",
    introduction:
      "Research Methodology provides a comprehensive framework for designing, conducting, and evaluating research in various disciplines. This course covers the entire research process from problem formulation to data collection, analysis, and reporting. Students will learn both quantitative and qualitative research approaches, ethical considerations, and best practices for producing rigorous and impactful research.",
    content: [
      {
        title: "Module 1: Foundations of Research",
        content: [
          "Philosophy of science and research paradigms",
          "Types of research: exploratory, descriptive, explanatory, and causal",
          "Quantitative, qualitative, and mixed methods approaches",
          "Research ethics and integrity",
          "Literature review and theoretical frameworks",
        ],
      },
      {
        title: "Module 2: Research Design",
        content: [
          "Experimental designs",
          "Quasi-experimental designs",
          "Survey research",
          "Case studies and observational research",
          "Longitudinal and cross-sectional designs",
        ],
      },
      {
        title: "Module 3: Data Collection Methods",
        content: [
          "Sampling techniques and sample size determination",
          "Questionnaire design and validation",
          "Interview methods and protocols",
          "Observational techniques",
          "Secondary data sources and evaluation",
        ],
      },
      {
        title: "Module 4: Data Analysis Approaches",
        content: [
          "Quantitative data analysis methods",
          "Qualitative data analysis techniques",
          "Mixed methods analysis strategies",
          "Statistical software applications",
          "Interpretation and presentation of findings",
        ],
      },
      {
        title: "Module 5: Research Communication",
        content: [
          "Academic writing and publication process",
          "Research proposal development",
          "Thesis and dissertation structure",
          "Presenting research findings",
          "Research impact and dissemination strategies",
        ],
      },
    ],
    assignments: [
      "Research proposal development",
      "Literature review on a selected topic",
      "Questionnaire design and pilot testing",
      "Data collection and analysis mini-project",
      "Research paper preparation following journal guidelines",
    ],
    caseStudies: [
      "Ethical challenges in medical research",
      "Mixed methods research in public health",
      "Longitudinal studies in educational research",
      "Experimental design in clinical trials",
      "Survey research in social sciences",
    ],
    datasets: [
      {
        name: "General Social Survey",
        description: "Demographic and attitudinal data for social research",
      },
      {
        name: "Demographic and Health Surveys",
        description: "Population health data from developing countries",
      },
      {
        name: "National Health and Nutrition Examination Survey",
        description: "Health and nutritional status of US population",
      },
      {
        name: "World Values Survey",
        description: "Global research on values and beliefs",
      },
    ],
    textbooks: [
      {
        title: "Research Design: Qualitative, Quantitative, and Mixed Methods Approaches",
        author: "John W. Creswell and J. David Creswell",
        link: "https://www.amazon.com/Research-Design-Qualitative-Quantitative-Approaches/dp/**********",
      },
      {
        title: "The Craft of Research",
        author: "Wayne C. Booth, Gregory G. Colomb, and Joseph M. Williams",
        link: "https://www.amazon.com/Research-Chicago-Writing-Editing-Publishing/dp/**********",
      },
      {
        title: "Qualitative Research & Evaluation Methods",
        author: "Michael Quinn Patton",
        link: "https://www.amazon.com/Qualitative-Research-Evaluation-Methods-Michael/dp/**********",
      },
      {
        title: "Discovering Statistics Using R",
        author: "Andy Field, Jeremy Miles, and Zoë Field",
        link: "https://www.amazon.com/Discovering-Statistics-Using-Andy-Field/dp/1446200469",
      },
    ],
  },
  {
    id: 6,
    slug: "advanced-statistical-methods",
    title: "Advanced Statistical Methods",
    image: "/3d-graph.png",
    description:
      "Explore advanced statistical techniques including multivariate analysis, time series analysis, and machine learning applications.",
    introduction:
      "Advanced Statistical Methods builds upon foundational statistical knowledge to explore sophisticated analytical techniques used in modern research and data science. This course covers multivariate methods, time series analysis, machine learning approaches, and other advanced statistical techniques. Students will develop the skills to tackle complex data analysis challenges and extract meaningful insights from multidimensional data.",
    content: [
      {
        title: "Module 1: Multivariate Analysis",
        content: [
          "Multivariate data visualization",
          "Principal Component Analysis (PCA)",
          "Factor Analysis",
          "Discriminant Analysis",
          "Canonical Correlation Analysis",
        ],
      },
      {
        title: "Module 2: Time Series Analysis",
        content: [
          "Time series components and decomposition",
          "Stationarity and transformations",
          "ARIMA modeling",
          "Spectral analysis",
          "State space models and Kalman filtering",
        ],
      },
      {
        title: "Module 3: Machine Learning Methods",
        content: [
          "Supervised vs. unsupervised learning",
          "Classification and regression trees",
          "Random forests and boosting methods",
          "Support vector machines",
          "Neural networks and deep learning introduction",
        ],
      },
      {
        title: "Module 4: Bayesian Statistics",
        content: [
          "Bayesian inference principles",
          "Prior and posterior distributions",
          "Markov Chain Monte Carlo methods",
          "Bayesian hierarchical models",
          "Bayesian model selection and averaging",
        ],
      },
      {
        title: "Module 5: Advanced Regression Techniques",
        content: [
          "Generalized linear models",
          "Mixed effects models",
          "Nonlinear regression",
          "Quantile regression",
          "Regularization methods (Ridge, Lasso, Elastic Net)",
        ],
      },
    ],
    assignments: [
      "Multivariate analysis of complex dataset",
      "Time series forecasting project",
      "Machine learning model development and evaluation",
      "Bayesian analysis implementation",
      "Advanced regression modeling case study",
    ],
    caseStudies: [
      "Gene expression analysis using multivariate methods",
      "Economic forecasting with advanced time series models",
      "Medical diagnosis using machine learning algorithms",
      "Environmental data analysis with spatial-temporal models",
      "Customer behavior prediction using advanced regression",
    ],
    datasets: [
      {
        name: "Gene Expression Omnibus",
        description: "Genomic data for multivariate analysis",
      },
      {
        name: "Federal Reserve Economic Data (FRED)",
        description: "Economic time series for forecasting",
      },
      {
        name: "UCI Machine Learning Repository",
        description: "Datasets for machine learning applications",
      },
      {
        name: "ImageNet",
        description: "Image database for deep learning applications",
      },
    ],
    textbooks: [
      {
        title: "Applied Multivariate Statistical Analysis",
        author: "Richard A. Johnson and Dean W. Wichern",
        link: "https://www.amazon.com/Applied-Multivariate-Statistical-Analysis-Johnson/dp/**********",
      },
      {
        title: "Time Series Analysis: Forecasting and Control",
        author: "George E. P. Box, Gwilym M. Jenkins, Gregory C. Reinsel, and Greta M. Ljung",
        link: "https://www.amazon.com/Time-Analysis-Forecasting-Control-Statistics/dp/1118675029",
      },
      {
        title: "The Elements of Statistical Learning",
        author: "Trevor Hastie, Robert Tibshirani, and Jerome Friedman",
        link: "https://web.stanford.edu/~hastie/ElemStatLearn/",
      },
      {
        title: "Bayesian Data Analysis",
        author: "Andrew Gelman, John B. Carlin, Hal S. Stern, David B. Dunson, Aki Vehtari, and Donald B. Rubin",
        link: "http://www.stat.columbia.edu/~gelman/book/",
      },
    ],
  },
]

// Generate static paths for each course slug
export async function generateStaticParams() {
  return courses.map((course) => ({
    slug: course.slug,
  }))
}

export default function CoursePage({ params }: { params: { slug: string } }) {
  const course = courses.find((c) => c.slug === params.slug)

  if (!course) {
    notFound()
  }

  return (
    <div className="py-12">
      <div className="container mx-auto px-4">
        {/* Breadcrumb */}
        <div className="mb-8">
          <Link
            href="/courses"
            className="flex items-center text-[#4ecdc4] hover:text-[#0a2158] transition-colors duration-200"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to All Courses
          </Link>
        </div>

        {/* Course Header */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden mb-12">
          <div className="relative h-64 md:h-80">
            <Image src={course.image || "/placeholder.svg"} alt={course.title} fill className="object-cover" />
          </div>
          <div className="p-8">
            <h1 className="text-3xl font-bold mb-4">{course.title}</h1>
            <p className="text-gray-700 mb-6">{course.description}</p>
          </div>
        </div>

        {/* Course Introduction */}
        <section className="mb-12">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-bold mb-6 flex items-center">
              <BookOpen className="h-6 w-6 text-[#f5a623] mr-3" />
              Course Introduction
            </h2>
            <p className="text-gray-700 leading-relaxed">{course.introduction}</p>
          </div>
        </section>

        {/* Course Content */}
        <section className="mb-12">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-bold mb-6 flex items-center">
              <FileText className="h-6 w-6 text-[#f5a623] mr-3" />
              Course Content
            </h2>
            <div className="space-y-6">
              {course.content.map((module, index) => (
                <div key={index} className="border-l-4 border-[#4ecdc4] pl-4 py-2">
                  <h3 className="text-xl font-bold mb-3">{module.title}</h3>
                  <ul className="space-y-2">
                    {module.content.map((item, idx) => (
                      <li key={idx} className="flex items-start">
                        <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Assignments */}
        <section className="mb-12">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-bold mb-6 flex items-center">
              <FileText className="h-6 w-6 text-[#f5a623] mr-3" />
              Assignments
            </h2>
            <ul className="space-y-3">
              {course.assignments.map((assignment, index) => (
                <li key={index} className="flex items-start">
                  <div className="h-6 w-6 rounded-full bg-[#0a2158] flex items-center justify-center text-white font-bold mr-3 mt-0.5">
                    {index + 1}
                  </div>
                  <span className="text-gray-700">{assignment}</span>
                </li>
              ))}
            </ul>
          </div>
        </section>

        {/* Case Studies */}
        <section className="mb-12">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-bold mb-6 flex items-center">
              <Book className="h-6 w-6 text-[#f5a623] mr-3" />
              Case Studies
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {course.caseStudies.map((caseStudy, index) => (
                <div key={index} className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-bold mb-2">Case Study {index + 1}</h3>
                  <p className="text-gray-700">{caseStudy}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Datasets */}
        <section className="mb-12">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-bold mb-6 flex items-center">
              <Database className="h-6 w-6 text-[#f5a623] mr-3" />
              Datasets
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {course.datasets.map((dataset, index) => (
                <div key={index} className="border border-gray-200 p-4 rounded-lg">
                  <h3 className="font-bold mb-2">{dataset.name}</h3>
                  <p className="text-gray-700 text-sm">{dataset.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Recommended Textbooks */}
        <section className="mb-12">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-bold mb-6 flex items-center">
              <Book className="h-6 w-6 text-[#f5a623] mr-3" />
              Recommended Textbooks
            </h2>
            <div className="space-y-6">
              {course.textbooks.map((book, index) => (
                <div key={index} className="flex items-start">
                  <div className="bg-[#4ecdc4] p-3 rounded-lg mr-4">
                    <Book className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-bold mb-1">{book.title}</h3>
                    <p className="text-gray-600 mb-2">by {book.author}</p>
                    {book.link && (
                      <a
                        href={book.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-[#f5a623] hover:underline text-sm"
                      >
                        View Book Details
                      </a>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Registration CTA */}
        <div className="bg-gradient-to-r from-[#0a2158] to-[#1a3a7a] text-white rounded-lg shadow-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to Enroll in this Course?</h2>
          <p className="mb-6 max-w-2xl mx-auto">
            Gain valuable skills and knowledge in {course.title}. Contact us today to learn about enrollment options,
            schedules, and more.
          </p>
          <Link
            href="/contact"
            className="bg-[#f5a623] hover:bg-[#e09612] text-white font-bold py-3 px-8 rounded-full transition duration-300 inline-block"
          >
            Contact for Enrollment
          </Link>
        </div>
      </div>
    </div>
  )
}
