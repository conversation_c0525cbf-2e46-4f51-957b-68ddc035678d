"use client"

import Image from "next/image"
import { CheckCircle } from "lucide-react"
import { motion } from "framer-motion"
import LinkWrapper from "@/components/link-wrapper"

export default function StatisticalConsultancyPage() {
  const cardHover = {
    rest: { scale: 1 },
    hover: {
      scale: 1.03,
      boxShadow: "0 10px 30px rgba(0,0,0,0.1)",
      transition: {
        duration: 0.3,
        ease: "easeInOut",
      },
    },
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  }

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-[#0a2158] to-[#1a3a7a] text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Statistical Consultancy Services</h1>
          <p className="text-xl max-w-3xl mx-auto">
            Expert statistical support for researchers, businesses, and organizations
          </p>
        </div>
      </section>

      {/* Overview Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-2xl font-bold mb-6">Professional Statistical Consultancy</h2>
              <p className="text-gray-700 mb-4">
                With over 15 years of experience providing statistical consultancy services, we offer comprehensive
                support for all your statistical needs. Our expertise spans across various domains including medical
                research, clinical trials, public health studies, and more.
              </p>
              <p className="text-gray-700 mb-4">
                Our consultancy services are designed to help researchers, healthcare professionals, and organizations
                make informed decisions based on sound statistical analysis and interpretation of data.
              </p>
              <div className="mt-6">
                <LinkWrapper
                  href="/contact"
                  className="bg-[#f5a623] hover:bg-[#e09612] text-white font-bold py-2 px-6 rounded transition duration-300"
                >
                  Request Consultation
                </LinkWrapper>
              </div>
            </div>
            <div>
              <Image
                src="/images/graph.png"
                alt="Statistical Consultancy"
                width={500}
                height={400}
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-12 text-center">Our Consultancy Services</h2>

          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {/* Service 1 */}
            <motion.div variants={fadeInUp} custom={0} className="bg-white p-6 rounded-lg shadow-md">
              <motion.div variants={cardHover} initial="rest" whileHover="hover" className="h-full">
                <h3 className="text-xl font-bold mb-4">Study Design</h3>
                <p className="text-gray-600 mb-4">
                  Expert guidance on research design, sample size calculation, and methodology to ensure robust and
                  valid results.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Sample size determination</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Research design optimization</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Methodology development</span>
                  </li>
                </ul>
              </motion.div>
            </motion.div>

            {/* Service 2 */}
            <motion.div variants={fadeInUp} custom={1} className="bg-white p-6 rounded-lg shadow-md">
              <motion.div variants={cardHover} initial="rest" whileHover="hover" className="h-full">
                <h3 className="text-xl font-bold mb-4">Data Analysis</h3>
                <p className="text-gray-600 mb-4">
                  Comprehensive statistical analysis using advanced techniques and software to extract meaningful
                  insights from your data.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Descriptive statistics</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Inferential statistics</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Advanced modeling techniques</span>
                  </li>
                </ul>
              </motion.div>
            </motion.div>

            {/* Service 3 */}
            <motion.div variants={fadeInUp} custom={2} className="bg-white p-6 rounded-lg shadow-md">
              <motion.div variants={cardHover} initial="rest" whileHover="hover" className="h-full">
                <h3 className="text-xl font-bold mb-4">Results Interpretation</h3>
                <p className="text-gray-600 mb-4">
                  Clear explanation and interpretation of statistical results to help you understand the implications
                  for your research or business.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Contextual interpretation</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Visualization of findings</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Practical recommendations</span>
                  </li>
                </ul>
              </motion.div>
            </motion.div>

            {/* Service 4 */}
            <motion.div variants={fadeInUp} custom={3} className="bg-white p-6 rounded-lg shadow-md">
              <motion.div variants={cardHover} initial="rest" whileHover="hover" className="h-full">
                <h3 className="text-xl font-bold mb-4">KPI Development</h3>
                <p className="text-gray-600 mb-4">
                  Development of key performance indicators and metrics to monitor and evaluate performance and
                  outcomes.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Custom KPI development</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Performance monitoring systems</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Benchmark analysis</span>
                  </li>
                </ul>
              </motion.div>
            </motion.div>

            {/* Service 5 */}
            <motion.div variants={fadeInUp} custom={4} className="bg-white p-6 rounded-lg shadow-md">
              <motion.div variants={cardHover} initial="rest" whileHover="hover" className="h-full">
                <h3 className="text-xl font-bold mb-4">Statistical Training</h3>
                <p className="text-gray-600 mb-4">
                  Customized training sessions and workshops on statistical methods and software for researchers and
                  professionals.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Software training (SPSS, R, SAS)</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Statistical methods workshops</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Customized training programs</span>
                  </li>
                </ul>
              </motion.div>
            </motion.div>

            {/* Service 6 */}
            <motion.div variants={fadeInUp} custom={5} className="bg-white p-6 rounded-lg shadow-md">
              <motion.div variants={cardHover} initial="rest" whileHover="hover" className="h-full">
                <h3 className="text-xl font-bold mb-4">Report Writing</h3>
                <p className="text-gray-600 mb-4">
                  Professional statistical report writing for research papers, grant applications, and business reports.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Research paper assistance</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Grant application support</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                    <span>Technical report preparation</span>
                  </li>
                </ul>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-12 text-center">What Our Clients Say</h2>

          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {/* Testimonial 1 */}
            <motion.div variants={fadeInUp} custom={0}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md"
              >
                <div className="flex items-center mb-4">
                  <div className="h-12 w-12 rounded-full bg-gray-300 mr-4"></div>
                  <div>
                    <h4 className="font-bold">Dr. Sarah Johnson</h4>
                    <p className="text-sm text-gray-500">Medical Researcher</p>
                  </div>
                </div>
                <p className="text-gray-600 italic">
                  "The statistical consultancy services provided were exceptional. The analysis and interpretation of
                  our research data significantly improved the quality of our publication."
                </p>
              </motion.div>
            </motion.div>

            {/* Testimonial 2 */}
            <motion.div variants={fadeInUp} custom={1}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md"
              >
                <div className="flex items-center mb-4">
                  <div className="h-12 w-12 rounded-full bg-gray-300 mr-4"></div>
                  <div>
                    <h4 className="font-bold">Prof. Michael Chen</h4>
                    <p className="text-sm text-gray-500">University Department Head</p>
                  </div>
                </div>
                <p className="text-gray-600 italic">
                  "The KPI development and monitoring system implemented has transformed how we track and evaluate our
                  department's performance. Highly recommended."
                </p>
              </motion.div>
            </motion.div>

            {/* Testimonial 3 */}
            <motion.div variants={fadeInUp} custom={2}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md"
              >
                <div className="flex items-center mb-4">
                  <div className="h-12 w-12 rounded-full bg-gray-300 mr-4"></div>
                  <div>
                    <h4 className="font-bold">Dr. Ahmed Al-Farsi</h4>
                    <p className="text-sm text-gray-500">Clinical Researcher</p>
                  </div>
                </div>
                <p className="text-gray-600 italic">
                  "The statistical training workshops were comprehensive and practical. Our research team now has a much
                  better understanding of statistical methods and their applications."
                </p>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-[#0a2158] text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-2xl font-bold mb-6">Ready to Get Started?</h2>
          <p className="text-xl max-w-3xl mx-auto mb-8">
            Contact us today to discuss your statistical consultancy needs and how we can help you achieve your research
            or business goals.
          </p>
          <LinkWrapper
            href="/contact"
            className="bg-[#f5a623] hover:bg-[#e09612] text-white font-bold py-3 px-8 rounded-full transition duration-300"
          >
            Request a Consultation
          </LinkWrapper>
        </div>
      </section>
    </div>
  )
}
