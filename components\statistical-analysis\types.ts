// Data types
export interface DataRow {
  [key: string]: string | number
}

export interface DataSet {
  headers: string[]
  rows: DataRow[]
  originalRows?: DataRow[] // For storing original data when filtering
  columnTypes?: Record<string, "numeric" | "categorical"> // Store column types
}

// Data management types
export interface SortConfig {
  column: string
  direction: "asc" | "desc"
}

export interface FilterConfig {
  column: string
  value: string
  operator: "contains" | "equals" | "greater" | "less"
}

export interface RecodeConfig {
  column: string
  oldValue: string
  newValue: string
}

export interface TransformConfig {
  column: string
  operation: "log" | "sqrt" | "square" | "standardize" | "center" | "custom"
  customFormula?: string
}

// Analysis types
export type AnalysisType =
  | "descriptive-numeric"
  | "descriptive-categorical"
  | "crosstab"
  | "ttest-independent"
  | "ttest-paired"
  | "anova"
  | "correlation"

export interface AnalysisResult {
  id: string
  title: string
  content: string
  tableData?: {
    headers: string[]
    rows: any[][]
  }
  additionalData?: any
}

// Chart types
export type ChartType = "bar" | "pie" | "histogram" | "boxplot" | "scatter" | "clustered-bar"

export interface ChartResult {
  id: string
  chartId: string
  title: string
  type: ChartType
  variables: string[]
}

// Statistics types
export interface NumericStats {
  n: number
  min: number
  max: number
  range: number
  mean: number
  median: number
  q1: number
  q3: number
  iqr: number
  variance: number
  stdDev: number
  stdError: number
  skewness: number
  kurtosis: number
  normality: {
    shapiroWilk?: {
      statistic: number
      pValue: number
    }
  }
}
