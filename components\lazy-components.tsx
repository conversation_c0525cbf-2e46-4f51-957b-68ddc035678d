"use client"

import dynamic from "next/dynamic"
import { Suspense } from "react"

// Loading components
const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#4ecdc4]"></div>
  </div>
)

const LoadingCard = () => (
  <div className="bg-white rounded-lg shadow-lg p-8 animate-pulse">
    <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
    <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
    <div className="h-4 bg-gray-200 rounded w-2/3"></div>
  </div>
)

const LoadingForm = () => (
  <div className="bg-white rounded-lg shadow-lg p-8 animate-pulse">
    <div className="space-y-4">
      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
      <div className="h-10 bg-gray-200 rounded"></div>
      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
      <div className="h-10 bg-gray-200 rounded"></div>
      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
      <div className="h-24 bg-gray-200 rounded"></div>
      <div className="h-10 bg-gray-200 rounded w-1/3"></div>
    </div>
  </div>
)

// Lazy loaded components
export const LazyContactForm = dynamic(() => import("./contact-form"), {
  loading: () => <LoadingForm />,
  ssr: false,
})

export const LazyStatisticalAnalysisApp = dynamic(() => import("./statistical-analysis-app"), {
  loading: () => <LoadingSpinner />,
  ssr: false,
})

export const LazyCounter = dynamic(() => import("./counter"), {
  loading: () => <div className="text-2xl font-bold text-white">0</div>,
  ssr: false,
})

// Lazy chart components
export const LazyChartResult = dynamic(() => import("./statistical-analysis/chart-result"), {
  loading: () => <LoadingCard />,
  ssr: false,
})

export const LazyAnalysisResult = dynamic(() => import("./statistical-analysis/analysis-result"), {
  loading: () => <LoadingCard />,
  ssr: false,
})

// Wrapper component for intersection observer based lazy loading
export function LazyLoadOnScroll({ 
  children, 
  threshold = 0.1,
  rootMargin = "50px"
}: { 
  children: React.ReactNode
  threshold?: number
  rootMargin?: string
}) {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      {children}
    </Suspense>
  )
}
