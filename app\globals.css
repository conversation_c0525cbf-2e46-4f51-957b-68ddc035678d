@tailwind base;
@tailwind components;
@tailwind utilities;

/* Critical CSS - Above the fold styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-feature-settings: 'rlig' 1, 'calt' 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Prevent layout shift */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Critical navigation styles */
  nav {
    contain: layout style paint;
  }

  /* Critical hero section styles */
  .hero-section {
    min-height: 100vh;
    contain: layout style paint;
  }
}

/* Performance-optimized animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-down {
  from {
    height: 0;
    opacity: 0;
  }
  to {
    height: auto;
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out forwards;
}

/* Specific animation for dropdown */
.dropdown-fade-in {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
  animation: dropdown-appear 0.2s ease-out forwards;
}

@keyframes dropdown-appear {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced dropdown styling */
.dropdown-menu {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  z-index: 100000 !important;
  position: relative;
}

.dropdown-menu-item {
  color: #374151;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.dropdown-menu-item:hover {
  background: linear-gradient(90deg, rgba(78, 237, 196, 0.1) 0%, rgba(78, 237, 196, 0.05) 100%);
  color: #0a2158;
  transform: translateX(2px);
}

/* Force Services dropdown to appear above everything */
nav [class*="absolute"][class*="top-full"] {
  z-index: 100001 !important;
  position: absolute !important;
  isolation: isolate;
}

.animate-slide-down {
  animation: slide-down 0.3s ease-out forwards;
}

/* Hero section animations */
@keyframes hero-fade-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

.animate-hero-fade-up {
  opacity: 0;
  animation: hero-fade-up 1s ease-out forwards;
}

.animate-float {
  animation: float 12s ease-in-out infinite;
}

/* Background pattern using CSS instead of external image */
.bg-hero-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(74, 237, 196, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(245, 166, 35, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(10, 33, 88, 0.1) 0%, transparent 50%);
  background-size: 400px 400px, 300px 300px, 500px 500px;
  background-position: 0 0, 100px 100px, 200px 200px;
}

/* Performance optimizations */
@layer utilities {
  .will-change-transform {
    will-change: transform;
  }

  .will-change-opacity {
    will-change: opacity;
  }

  .contain-layout {
    contain: layout;
  }

  .contain-paint {
    contain: paint;
  }

  .contain-strict {
    contain: strict;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    .animate-fade-in,
    .animate-slide-down,
    .animate-hero-fade-up,
    .animate-float {
      animation: none;
    }

    .transition-transform,
    .transition-all {
      transition: none;
    }
  }
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-rgb: 255, 255, 255;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
}

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-bold;
  }
}

/* Animation classes */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-slideUp {
  animation: slideUp 0.5s ease-out;
}

.animate-slideDown {
  animation: slideDown 0.5s ease-out;
}

.animate-slideLeft {
  animation: slideLeft 0.5s ease-out;
}

.animate-slideRight {
  animation: slideRight 0.5s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced service card animations - GPU accelerated */
.animate-card-entrance {
  animation: cardEntrance 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
  will-change: opacity, transform;
}

@keyframes cardEntrance {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered animation delays */
.animate-card-entrance:nth-child(1) { animation-delay: 0.1s; }
.animate-card-entrance:nth-child(2) { animation-delay: 0.2s; }
.animate-card-entrance:nth-child(3) { animation-delay: 0.3s; }
.animate-card-entrance:nth-child(4) { animation-delay: 0.4s; }
.animate-card-entrance:nth-child(5) { animation-delay: 0.5s; }
.animate-card-entrance:nth-child(6) { animation-delay: 0.6s; }

/* Performance optimizations for service cards */
.service-card {
  contain: layout style paint;
  transform: translateZ(0); /* Force GPU acceleration */
}

.service-card:hover {
  will-change: transform, box-shadow;
}

.service-card:not(:hover) {
  will-change: auto;
}

/* Smooth focus indicators for accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-white;
}

/* Enhanced hover effects */
.card-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #0a2158 0%, #4ecdc4 50%, #f5a623 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideLeft {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideRight {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(-10px);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}
