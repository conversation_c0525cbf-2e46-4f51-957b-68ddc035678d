"use client"

import { lazy, Suspense } from "react"

// Lazy load framer-motion only when needed
const MotionDiv = lazy(() => 
  import("framer-motion").then(module => ({ 
    default: module.motion.div 
  }))
)

interface MotionWrapperProps {
  children: React.ReactNode
  className?: string
  variants?: any
  initial?: string | object
  animate?: string | object
  whileHover?: string | object
  whileTap?: string | object
  transition?: object
  style?: React.CSSProperties
  onClick?: () => void
}

// Fallback component that uses CSS animations instead of framer-motion
function CSSMotionDiv({ 
  children, 
  className = "", 
  whileHover,
  onClick,
  style,
  ...props 
}: MotionWrapperProps) {
  const hoverClass = whileHover ? "hover:scale-105 transition-transform duration-300" : ""
  
  return (
    <div 
      className={`${className} ${hoverClass}`}
      onClick={onClick}
      style={style}
    >
      {children}
    </div>
  )
}

// Main wrapper that decides whether to use framer-motion or CSS
export default function MotionWrapper(props: MotionWrapperProps) {
  // For simple hover effects, use CSS instead of framer-motion
  const isSimpleAnimation = props.whileHover && !props.variants && !props.animate
  
  if (isSimpleAnimation) {
    return <CSSMotionDiv {...props} />
  }

  // For complex animations, lazy load framer-motion
  return (
    <Suspense fallback={<CSSMotionDiv {...props} />}>
      <MotionDiv {...props} />
    </Suspense>
  )
}

// Utility function to create optimized card hover effects
export function createCardHoverVariants() {
  return {
    rest: { scale: 1 },
    hover: {
      scale: 1.03,
      boxShadow: "0 10px 30px rgba(0,0,0,0.1)",
      transition: {
        duration: 0.3,
        ease: "easeInOut",
      },
    },
  }
}

// CSS-only card hover component
export function CardHover({ 
  children, 
  className = "" 
}: { 
  children: React.ReactNode
  className?: string 
}) {
  return (
    <div className={`transition-all duration-300 hover:scale-[1.03] hover:shadow-xl ${className}`}>
      {children}
    </div>
  )
}
