"use client"

import { Facebook, Twitter, Youtube, Linkedin } from "lucide-react"
import { motion } from "framer-motion"
import LinkWrapper from "./link-wrapper"

export default function Footer() {
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  }

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  return (
    <footer className="bg-[#0a2158] text-white py-8">
      <div className="container mx-auto px-4">
        <motion.div
          variants={staggerContainer}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          <motion.div variants={fadeInUp}>
            <h3 className="text-xl font-bold mb-4">About Us</h3>
            <p className="mb-4">
              NSBSTAT provides statistical consultancy, research support, and educational services in the field of
              biostatistics.
            </p>
            <div className="flex space-x-4">
              <motion.div whileHover={{ scale: 1.2 }} whileTap={{ scale: 0.9 }}>
                <LinkWrapper
                  href="https://www.facebook.com/nadeem.shafique.pk/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-[#4ecdc4]"
                >
                  <Facebook size={20} />
                </LinkWrapper>
              </motion.div>
              <motion.div whileHover={{ scale: 1.2 }} whileTap={{ scale: 0.9 }}>
                <LinkWrapper
                  href="https://x.com/nadeemshafique"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-[#4ecdc4]"
                >
                  <Twitter size={20} />
                </LinkWrapper>
              </motion.div>
              <motion.div whileHover={{ scale: 1.2 }} whileTap={{ scale: 0.9 }}>
                <LinkWrapper
                  href="https://www.youtube.com/nadeemshafique"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-[#4ecdc4]"
                >
                  <Youtube size={20} />
                </LinkWrapper>
              </motion.div>
              <motion.div whileHover={{ scale: 1.2 }} whileTap={{ scale: 0.9 }}>
                <LinkWrapper
                  href="https://www.linkedin.com/in/nadeem-shafique-butt-99a41418/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:text-[#4ecdc4]"
                >
                  <Linkedin size={20} />
                </LinkWrapper>
              </motion.div>
            </div>
          </motion.div>

          <motion.div variants={fadeInUp}>
            <h3 className="text-xl font-bold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <motion.li whileHover={{ x: 5 }} transition={{ duration: 0.2 }}>
                <LinkWrapper href="/" className="hover:text-[#4ecdc4]">
                  Home
                </LinkWrapper>
              </motion.li>
              <motion.li whileHover={{ x: 5 }} transition={{ duration: 0.2 }}>
                <LinkWrapper href="/courses" className="hover:text-[#4ecdc4]">
                  Courses
                </LinkWrapper>
              </motion.li>
              <motion.li whileHover={{ x: 5 }} transition={{ duration: 0.2 }}>
                <LinkWrapper href="/statistical-consultancy" className="hover:text-[#4ecdc4]">
                  Statistical Consultancy
                </LinkWrapper>
              </motion.li>
              <motion.li whileHover={{ x: 5 }} transition={{ duration: 0.2 }}>
                <LinkWrapper href="/surveys" className="hover:text-[#4ecdc4]">
                  Survey Administration
                </LinkWrapper>
              </motion.li>
              <motion.li whileHover={{ x: 5 }} transition={{ duration: 0.2 }}>
                <LinkWrapper href="/research" className="hover:text-[#4ecdc4]">
                  Research
                </LinkWrapper>
              </motion.li>
              <motion.li whileHover={{ x: 5 }} transition={{ duration: 0.2 }}>
                <LinkWrapper href="/resume" className="hover:text-[#4ecdc4]">
                  Resume
                </LinkWrapper>
              </motion.li>
            </ul>
          </motion.div>

          <motion.div variants={fadeInUp}>
            <h3 className="text-xl font-bold mb-4">Contact Info</h3>
            <p className="mb-2">Department of Family and Community Medicine</p>
            <p className="mb-2">King Abdulaziz University, KSA</p>
            <p className="mb-2">Email: <EMAIL></p>
            <p>Phone: +92-312-4441234</p>
          </motion.div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5, duration: 0.6 }}
          className="border-t border-gray-700 mt-8 pt-6 text-center"
        >
          <p>&copy; {new Date().getFullYear()} NSBSTAT. All rights reserved.</p>
        </motion.div>
      </div>
    </footer>
  )
}
