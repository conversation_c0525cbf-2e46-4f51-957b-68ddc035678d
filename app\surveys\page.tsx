"use client"

import Image from "next/image"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import { motion } from "framer-motion"
import LinkWrapper from "@/components/link-wrapper"

export default function SurveysPage() {
  const cardHover = {
    rest: { scale: 1 },
    hover: {
      scale: 1.03,
      boxShadow: "0 10px 30px rgba(0,0,0,0.1)",
      transition: {
        duration: 0.3,
        ease: "easeInOut",
      },
    },
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  }

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-[#0a2158] to-[#1a3a7a] text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Survey Administration Services</h1>
          <p className="text-xl max-w-3xl mx-auto">
            Comprehensive survey design, implementation, and analysis for research and business needs
          </p>
        </div>
      </section>

      {/* Overview Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <Image
                src="/survey.jpeg"
                alt="Survey Administration"
                width={500}
                height={400}
                className="rounded-lg shadow-lg"
              />
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <h2 className="text-2xl font-bold mb-6">Professional Survey Services</h2>
              <p className="text-gray-700 mb-4">
                Our survey administration services provide end-to-end solutions for all your data collection needs. From
                survey design and implementation to data analysis and reporting, we offer comprehensive support to
                ensure high-quality, reliable results.
              </p>
              <p className="text-gray-700 mb-4">
                With expertise in various survey methodologies and tools, we help researchers, healthcare professionals,
                businesses, and organizations gather valuable insights through well-designed and properly executed
                surveys.
              </p>
              <div className="mt-6">
                <LinkWrapper
                  href="/contact"
                  className="bg-[#f5a623] hover:bg-[#e09612] text-white font-bold py-2 px-6 rounded transition duration-300"
                >
                  Discuss Your Survey Needs
                </LinkWrapper>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Survey Process Section */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-12 text-center">Our Survey Process</h2>

          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {/* Step 1 */}
            <motion.div variants={fadeInUp} custom={0}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md text-center"
              >
                <div className="flex justify-center mb-4">
                  <div className="bg-[#4ecdc4] p-3 rounded-full">
                    <span className="text-white text-xl font-bold">1</span>
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-4">Survey Design</h3>
                <p className="text-gray-600">
                  Expert design of survey instruments with validated questions and appropriate scales to collect
                  reliable data.
                </p>
              </motion.div>
            </motion.div>

            {/* Step 2 */}
            <motion.div variants={fadeInUp} custom={1}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md text-center"
              >
                <div className="flex justify-center mb-4">
                  <div className="bg-[#4ecdc4] p-3 rounded-full">
                    <span className="text-white text-xl font-bold">2</span>
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-4">Data Collection</h3>
                <p className="text-gray-600">
                  Implementation of surveys through various methods including online, telephone, in-person, and
                  mixed-mode approaches.
                </p>
              </motion.div>
            </motion.div>

            {/* Step 3 */}
            <motion.div variants={fadeInUp} custom={2}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md text-center"
              >
                <div className="flex justify-center mb-4">
                  <div className="bg-[#4ecdc4] p-3 rounded-full">
                    <span className="text-white text-xl font-bold">3</span>
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-4">Data Analysis</h3>
                <p className="text-gray-600">
                  Comprehensive statistical analysis of survey data using advanced techniques to extract meaningful
                  insights.
                </p>
              </motion.div>
            </motion.div>

            {/* Step 4 */}
            <motion.div variants={fadeInUp} custom={3}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md text-center"
              >
                <div className="flex justify-center mb-4">
                  <div className="bg-[#4ecdc4] p-3 rounded-full">
                    <span className="text-white text-xl font-bold">4</span>
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-4">Reporting</h3>
                <p className="text-gray-600">
                  Clear, comprehensive reports with visualizations and actionable recommendations based on survey
                  findings.
                </p>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-12 text-center">Our Survey Services</h2>

          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 gap-8"
          >
            {/* Service 1 */}
            <motion.div variants={fadeInUp} custom={0}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md flex"
              >
                <div className="mr-4">
                  <Users className="h-12 w-12 text-[#f5a623]" />
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-2">Population Surveys</h3>
                  <p className="text-gray-600 mb-4">
                    Large-scale surveys to collect data from specific populations for research, public health, or policy
                    purposes.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                      <span>Community health surveys</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                      <span>Demographic studies</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                      <span>Public opinion research</span>
                    </li>
                  </ul>
                </div>
              </motion.div>
            </motion.div>

            {/* Service 2 */}
            <motion.div variants={fadeInUp} custom={1}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md flex"
              >
                <div className="mr-4">
                  <BarChart className="h-12 w-12 text-[#f5a623]" />
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-2">Customer Satisfaction Surveys</h3>
                  <p className="text-gray-600 mb-4">
                    Surveys designed to measure customer satisfaction, identify areas for improvement, and track changes
                    over time.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                      <span>Service quality assessment</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                      <span>Product feedback collection</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                      <span>Net Promoter Score (NPS) surveys</span>
                    </li>
                  </ul>
                </div>
              </motion.div>
            </motion.div>

            {/* Service 3 */}
            <motion.div variants={fadeInUp} custom={2}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md flex"
              >
                <div className="mr-4">
                  <PieChart className="h-12 w-12 text-[#f5a623]" />
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-2">Employee Surveys</h3>
                  <p className="text-gray-600 mb-4">
                    Surveys to assess employee satisfaction, engagement, and organizational culture to improve workplace
                    environments.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                      <span>Employee engagement assessment</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                      <span>Workplace culture evaluation</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                      <span>Training needs analysis</span>
                    </li>
                  </ul>
                </div>
              </motion.div>
            </motion.div>

            {/* Service 4 */}
            <motion.div variants={fadeInUp} custom={3}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md flex"
              >
                <div className="mr-4">
                  <LineChart className="h-12 w-12 text-[#f5a623]" />
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-2">Research Surveys</h3>
                  <p className="text-gray-600 mb-4">
                    Specialized surveys for academic and scientific research across various disciplines.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                      <span>Clinical research surveys</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                      <span>Social science research</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-[#4ecdc4] mr-2 flex-shrink-0 mt-0.5" />
                      <span>Educational assessment surveys</span>
                    </li>
                  </ul>
                </div>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Tools Section */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-12 text-center">Survey Tools & Technologies</h2>

          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8"
          >
            {/* Tool 1 */}
            <motion.div variants={fadeInUp} custom={0}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md text-center"
              >
                <div className="h-16 flex items-center justify-center mb-4">
                  <Image src="/online-survey.jpeg" alt="REDCap" width={120} height={60} className="max-h-full" />
                </div>
                <h3 className="font-bold">REDCap</h3>
              </motion.div>
            </motion.div>

            {/* Tool 2 */}
            <motion.div variants={fadeInUp} custom={1}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md text-center"
              >
                <div className="h-16 flex items-center justify-center mb-4">
                  <Image src="/online-survey.jpeg" alt="Qualtrics" width={120} height={60} className="max-h-full" />
                </div>
                <h3 className="font-bold">Qualtrics</h3>
              </motion.div>
            </motion.div>

            {/* Tool 3 */}
            <motion.div variants={fadeInUp} custom={2}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md text-center"
              >
                <div className="h-16 flex items-center justify-center mb-4">
                  <Image src="/online-survey.jpeg" alt="SurveyMonkey" width={120} height={60} className="max-h-full" />
                </div>
                <h3 className="font-bold">SurveyMonkey</h3>
              </motion.div>
            </motion.div>

            {/* Tool 4 */}
            <motion.div variants={fadeInUp} custom={3}>
              <motion.div
                variants={cardHover}
                initial="rest"
                whileHover="hover"
                className="bg-white p-6 rounded-lg shadow-md text-center"
              >
                <div className="h-16 flex items-center justify-center mb-4">
                  <Image src="/online-survey.jpeg" alt="Google Forms" width={120} height={60} className="max-h-full" />
                </div>
                <h3 className="font-bold">Google Forms</h3>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-[#0a2158] text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-2xl font-bold mb-6">Need Help with Your Survey Project?</h2>
          <p className="text-xl max-w-3xl mx-auto mb-8">
            Contact us today to discuss your survey needs and how our expertise can help you collect high-quality data
            for your research or business.
          </p>
          <LinkWrapper
            href="/contact"
            className="bg-[#f5a623] hover:bg-[#e09612] text-white font-bold py-3 px-8 rounded-full transition duration-300"
          >
            Get Started
          </LinkWrapper>
        </div>
      </section>
    </div>
  )
}
