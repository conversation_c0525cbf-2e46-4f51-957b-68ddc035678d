import { v4 as uuidv4 } from "uuid"
import type { ChartType, DataSet, ChartResult } from "./types"

export const chartRegistry = [
  {
    type: "bar" as ChartType,
    name: "Bar Chart",
    description: "Display categorical data with rectangular bars",
    requiredVariableTypes: ["categorical", "numeric"],
    minVariables: 2,
    maxVariables: 2,
    execute: (dataset: DataSet, variables: string[]): ChartResult => {
      const chartId = `chart-${uuidv4()}`

      return {
        id: uuidv4(),
        chartId,
        title: "Bar Chart",
        type: "bar",
        variables,
      }
    },
  },
  {
    type: "pie" as ChartType,
    name: "Pie Chart",
    description: "Show proportion of categories in a circular graph",
    requiredVariableTypes: ["categorical", "numeric"],
    minVariables: 2,
    maxVariables: 2,
    execute: (dataset: DataSet, variables: string[]): ChartResult => {
      const chartId = `chart-${uuidv4()}`

      return {
        id: uuidv4(),
        chartId,
        title: "Pie Chart",
        type: "pie",
        variables,
      }
    },
  },
  {
    type: "histogram" as ChartType,
    name: "Histogram",
    description: "Display distribution of a numeric variable",
    requiredVariableTypes: ["numeric"],
    minVariables: 1,
    maxVariables: 1,
    execute: (dataset: DataSet, variables: string[]): ChartResult => {
      const chartId = `chart-${uuidv4()}`

      return {
        id: uuidv4(),
        chartId,
        title: "Histogram",
        type: "histogram",
        variables,
      }
    },
  },
  {
    type: "scatter" as ChartType,
    name: "Scatter Plot",
    description: "Show relationship between two numeric variables",
    requiredVariableTypes: ["numeric"],
    minVariables: 2,
    maxVariables: 2,
    execute: (dataset: DataSet, variables: string[]): ChartResult => {
      const chartId = `chart-${uuidv4()}`

      return {
        id: uuidv4(),
        chartId,
        title: "Scatter Plot",
        type: "scatter",
        variables,
      }
    },
  },
  // Add more chart types here
]

// Helper function to get chart by type
export function getChartByType(type: ChartType) {
  return chartRegistry.find((chart) => chart.type === type)
}
