const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const publicDir = path.join(__dirname, '../public');
const outputDir = path.join(publicDir, 'optimized');

// Create optimized directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// List of large images to optimize
const imagesToOptimize = [
  'statistical-computing.jpeg',
  'survival-analysis.jpeg',
  'workshops.jpeg',
  'survey.jpeg',
  'nsb-award.jpeg',
  'biostatistics.jpeg',
  'online-survey.jpeg'
];

async function optimizeImage(filename) {
  const inputPath = path.join(publicDir, filename);
  const name = path.parse(filename).name;
  const ext = path.parse(filename).ext;
  
  if (!fs.existsSync(inputPath)) {
    console.log(`File not found: ${filename}`);
    return;
  }

  try {
    // Generate WebP version
    await sharp(inputPath)
      .webp({ quality: 80, effort: 6 })
      .toFile(path.join(outputDir, `${name}.webp`));
    
    // Generate AVIF version
    await sharp(inputPath)
      .avif({ quality: 70, effort: 9 })
      .toFile(path.join(outputDir, `${name}.avif`));
    
    // Generate optimized JPEG
    await sharp(inputPath)
      .jpeg({ quality: 85, progressive: true, mozjpeg: true })
      .toFile(path.join(outputDir, `${name}-optimized${ext}`));
    
    // Generate different sizes for responsive images
    const sizes = [400, 800, 1200];
    for (const size of sizes) {
      await sharp(inputPath)
        .resize(size, null, { withoutEnlargement: true })
        .webp({ quality: 80 })
        .toFile(path.join(outputDir, `${name}-${size}w.webp`));
      
      await sharp(inputPath)
        .resize(size, null, { withoutEnlargement: true })
        .jpeg({ quality: 85, progressive: true })
        .toFile(path.join(outputDir, `${name}-${size}w.jpg`));
    }
    
    console.log(`✅ Optimized: ${filename}`);
  } catch (error) {
    console.error(`❌ Error optimizing ${filename}:`, error.message);
  }
}

async function main() {
  console.log('🖼️  Starting image optimization...');
  
  for (const image of imagesToOptimize) {
    await optimizeImage(image);
  }
  
  console.log('✨ Image optimization complete!');
}

main().catch(console.error);
