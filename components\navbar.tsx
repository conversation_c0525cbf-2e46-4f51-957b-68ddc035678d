"use client"

import { useState } from "react"
import Image from "next/image"
import { Menu, X, ChevronDown } from "lucide-react"
import Search from "./search"
import LinkWrapper from "./link-wrapper"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false)
  const [servicesOpen, setServicesOpen] = useState(false)

  // Simplified CSS-based animations instead of heavy framer-motion
  const navItemClass = "font-medium hover:text-[#4ecdc4] transition-colors duration-200 py-2 animate-fade-in"
  const logoClass = "transition-transform duration-300 hover:scale-105"

  return (
    <nav className="bg-white shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          <div className={logoClass}>
            <LinkWrapper href="/" className="flex items-center">
              <Image src="/logo.png" alt="NSBSTAT Logo" width={120} height={120} className="h-12 w-auto" />
            </LinkWrapper>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <LinkWrapper href="/" className={navItemClass} style={{ animationDelay: '0ms' }}>
              Home
            </LinkWrapper>

            <LinkWrapper href="/courses" className={navItemClass} style={{ animationDelay: '100ms' }}>
              Courses
            </LinkWrapper>

            <Popover>
              <PopoverTrigger asChild>
                <button
                  className={`flex items-center ${navItemClass}`}
                  style={{ animationDelay: '200ms' }}
                >
                  Services
                  <ChevronDown
                    className={`ml-1 h-4 w-4 transition-transform duration-200`}
                  />
                </button>
              </PopoverTrigger>
              <PopoverContent className="w-72">
                <div className="p-4 bg-gradient-to-r from-blue-600 to-blue-700 rounded-t-lg">
                  <h3 className="text-white font-bold">Our Services</h3>
                </div>

                <div className="p-2">
                  <LinkWrapper
                    href="/statistical-consultancy"
                    className="block p-4 rounded hover:bg-gray-100 transition-colors"
                  >
                    <div className="font-semibold text-gray-800">📊 Statistical Consultancy</div>
                    <div className="text-sm text-gray-600 mt-1">Expert research design & analysis</div>
                  </LinkWrapper>

                  <LinkWrapper
                    href="/surveys"
                    className="block p-4 rounded hover:bg-gray-100 transition-colors"
                  >
                    <div className="font-semibold text-gray-800">📋 Survey Administration</div>
                    <div className="text-sm text-gray-600 mt-1">Complete survey solutions</div>
                  </LinkWrapper>
                </div>
              </PopoverContent>
            </Popover>

            <LinkWrapper href="/research" className={navItemClass} style={{ animationDelay: '300ms' }}>
              Research
            </LinkWrapper>

            <LinkWrapper href="/resume" className={navItemClass} style={{ animationDelay: '400ms' }}>
              Resume
            </LinkWrapper>

            <LinkWrapper href="/contact" className={navItemClass} style={{ animationDelay: '500ms' }}>
              Contact
            </LinkWrapper>

            <LinkWrapper href="/statistical-analysis" className={navItemClass} style={{ animationDelay: '600ms' }}>
              Statistical Analysis
            </LinkWrapper>
          </div>

          <div className="hidden md:flex items-center">
            <Search />
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <Search />
            <button
              className="p-2 rounded-md hover:bg-gray-100 transition-colors duration-200 ml-2"
              onClick={() => setIsOpen(!isOpen)}
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden py-4 border-t border-gray-100 animate-slide-down">
            <div className="flex flex-col space-y-4">
              <LinkWrapper
                href="/"
                className="font-medium hover:text-[#4ecdc4] transition-colors duration-200 py-2"
                onClick={() => setIsOpen(false)}
              >
                Home
              </LinkWrapper>
              <LinkWrapper
                href="/courses"
                className="font-medium hover:text-[#4ecdc4] transition-colors duration-200 py-2"
                onClick={() => setIsOpen(false)}
              >
                Courses
              </LinkWrapper>
              <div>
                <button
                  className="flex items-center w-full font-medium hover:text-[#4ecdc4] transition-colors duration-200 py-2"
                  onClick={() => setServicesOpen(!servicesOpen)}
                >
                  Services
                  <ChevronDown
                    className={`ml-1 h-4 w-4 transition-transform duration-200 ${servicesOpen ? "rotate-180" : ""}`}
                  />
                </button>
                {servicesOpen && (
                  <div className="pl-4 mt-2 space-y-2 border-l-2 border-gray-100 ml-2 animate-fade-in">
                    <LinkWrapper
                      href="/statistical-consultancy"
                      className="block py-2 hover:text-[#4ecdc4] transition-colors duration-200"
                      onClick={() => {
                        setServicesOpen(false)
                        setIsOpen(false)
                      }}
                    >
                      Statistical Consultancy
                    </LinkWrapper>
                    <LinkWrapper
                      href="/surveys"
                      className="block py-2 hover:text-[#4ecdc4] transition-colors duration-200"
                      onClick={() => {
                        setServicesOpen(false)
                        setIsOpen(false)
                      }}
                    >
                      Survey Administration
                    </LinkWrapper>
                  </div>
                )}
              </div>
              <LinkWrapper
                href="/research"
                className="font-medium hover:text-[#4ecdc4] transition-colors duration-200 py-2"
                onClick={() => setIsOpen(false)}
              >
                Research
              </LinkWrapper>
              <LinkWrapper
                href="/resume"
                className="font-medium hover:text-[#4ecdc4] transition-colors duration-200 py-2"
                onClick={() => setIsOpen(false)}
              >
                Resume
              </LinkWrapper>
              <LinkWrapper
                href="/contact"
                className="font-medium hover:text-[#4ecdc4] transition-colors duration-200 py-2"
                onClick={() => setIsOpen(false)}
              >
                Contact
              </LinkWrapper>
              <LinkWrapper
                href="/statistical-analysis"
                className="font-medium hover:text-[#4ecdc4] transition-colors duration-200 py-2"
                onClick={() => setIsOpen(false)}
              >
                Statistical Analysis
              </LinkWrapper>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
