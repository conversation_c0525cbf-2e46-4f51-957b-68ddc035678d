"use client"

import { useState, useEffect, useRef } from "react"
import { X, Maximize2, ChevronDown, ChevronUp } from "lucide-react"
import type { ChartResult, DataSet } from "./types"
import * as echarts from "echarts"

interface ChartResultDisplayProps {
  result: ChartResult
  dataset: DataSet
  onRemove: (id: string) => void
}

export default function ChartResultDisplay({ result, dataset, onRemove }: ChartResultDisplayProps) {
  const [expanded, setExpanded] = useState(true)
  const [fullscreen, setFullscreen] = useState(false)
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  useEffect(() => {
    if (!chartRef.current) return

    // Initialize chart
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current)
    }

    // Prepare data based on chart type
    const prepareChartOptions = () => {
      const { type, variables } = result

      // Extract data for the selected variables
      const data = dataset.rows.map((row) => {
        const entry: Record<string, any> = {}
        variables.forEach((variable) => {
          entry[variable] = row[variable]
        })
        return entry
      })

      switch (type) {
        case "bar": {
          const categoryVar = variables[0]
          const valueVar = variables[1]

          // Group and aggregate data
          const aggregatedData: Record<string, number> = {}
          data.forEach((item) => {
            const category = String(item[categoryVar])
            const value = Number(item[valueVar])

            if (!isNaN(value)) {
              if (!aggregatedData[category]) {
                aggregatedData[category] = 0
              }
              aggregatedData[category] += value
            }
          })

          const categories = Object.keys(aggregatedData)
          const values = categories.map((cat) => aggregatedData[cat])

          return {
            title: {
              text: `${valueVar} by ${categoryVar}`,
              left: "center",
            },
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "shadow",
              },
            },
            xAxis: {
              type: "category",
              data: categories,
              name: categoryVar,
              axisLabel: {
                rotate: categories.length > 10 ? 45 : 0,
              },
            },
            yAxis: {
              type: "value",
              name: valueVar,
            },
            series: [
              {
                name: valueVar,
                type: "bar",
                data: values,
                itemStyle: {
                  color: "#2196f3",
                },
              },
            ],
          }
        }

        case "pie": {
          const categoryVar = variables[0]
          const valueVar = variables[1]

          // Group and aggregate data
          const aggregatedData: Record<string, number> = {}
          data.forEach((item) => {
            const category = String(item[categoryVar])
            const value = Number(item[valueVar])

            if (!isNaN(value)) {
              if (!aggregatedData[category]) {
                aggregatedData[category] = 0
              }
              aggregatedData[category] += value
            }
          })

          const pieData = Object.entries(aggregatedData).map(([name, value]) => ({ name, value }))

          return {
            title: {
              text: `${valueVar} by ${categoryVar}`,
              left: "center",
            },
            tooltip: {
              trigger: "item",
              formatter: "{a} <br/>{b}: {c} ({d}%)",
            },
            legend: {
              orient: "vertical",
              left: "left",
              data: pieData.map((item) => item.name),
            },
            series: [
              {
                name: valueVar,
                type: "pie",
                radius: "60%",
                center: ["50%", "60%"],
                data: pieData,
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: "rgba(0, 0, 0, 0.5)",
                  },
                },
              },
            ],
          }
        }

        case "histogram": {
          const valueVar = variables[0]

          // Extract numeric values
          const values = data.map((item) => Number(item[valueVar])).filter((val) => !isNaN(val))

          // Calculate bins
          const min = Math.min(...values)
          const max = Math.max(...values)
          const binCount = Math.min(20, Math.ceil(Math.sqrt(values.length)))
          const binWidth = (max - min) / binCount

          const bins = Array(binCount).fill(0)
          values.forEach((val) => {
            const binIndex = Math.min(binCount - 1, Math.floor((val - min) / binWidth))
            bins[binIndex]++
          })

          const binLabels = Array(binCount)
            .fill(0)
            .map((_, i) => {
              const start = min + i * binWidth
              const end = min + (i + 1) * binWidth
              return `${start.toFixed(2)}-${end.toFixed(2)}`
            })

          return {
            title: {
              text: `Histogram of ${valueVar}`,
              left: "center",
            },
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "shadow",
              },
            },
            xAxis: {
              type: "category",
              data: binLabels,
              name: valueVar,
              axisLabel: {
                rotate: 45,
              },
            },
            yAxis: {
              type: "value",
              name: "Frequency",
            },
            series: [
              {
                name: "Frequency",
                type: "bar",
                data: bins,
                itemStyle: {
                  color: "#2196f3",
                },
              },
            ],
          }
        }

        case "scatter": {
          const xVar = variables[0]
          const yVar = variables[1]

          // Extract paired values
          const scatterData = data
            .map((item) => [Number(item[xVar]), Number(item[yVar])])
            .filter((pair) => !isNaN(pair[0]) && !isNaN(pair[1]))

          return {
            title: {
              text: `${yVar} vs ${xVar}`,
              left: "center",
            },
            tooltip: {
              trigger: "item",
              formatter: (params: any) => `${xVar}: ${params.value[0]}<br/>${yVar}: ${params.value[1]}`,
            },
            xAxis: {
              type: "value",
              name: xVar,
            },
            yAxis: {
              type: "value",
              name: yVar,
            },
            series: [
              {
                name: `${yVar} vs ${xVar}`,
                type: "scatter",
                data: scatterData,
                symbolSize: 8,
                itemStyle: {
                  color: "#2196f3",
                },
              },
            ],
          }
        }

        default:
          return {
            title: {
              text: "Chart not implemented",
              left: "center",
            },
          }
      }
    }

    // Set chart options
    const options = prepareChartOptions()
    chartInstance.current.setOption(options)

    // Handle resize
    const handleResize = () => {
      chartInstance.current?.resize()
    }

    window.addEventListener("resize", handleResize)

    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [result, dataset, fullscreen])

  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    setFullscreen(!fullscreen)

    // Give the DOM time to update before resizing the chart
    setTimeout(() => {
      chartInstance.current?.resize()
    }, 100)
  }

  return (
    <>
      <div className="bg-white border rounded-lg shadow-sm mb-4 overflow-hidden">
        <div className="flex justify-between items-center p-4 border-b bg-gray-50">
          <div className="flex items-center">
            <button onClick={() => setExpanded(!expanded)} className="mr-2 text-gray-500 hover:text-gray-700">
              {expanded ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
            </button>
            <h3 className="font-bold text-lg">{result.title}</h3>
          </div>
          <div className="flex items-center">
            <button
              onClick={toggleFullscreen}
              className="text-gray-400 hover:text-gray-600 mr-2"
              aria-label="View fullscreen"
            >
              <Maximize2 className="h-5 w-5" />
            </button>
            <button
              onClick={() => onRemove(result.id)}
              className="text-gray-400 hover:text-gray-600"
              aria-label="Remove chart"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {expanded && (
          <div className="p-4">
            <div ref={chartRef} className="w-full" style={{ height: "400px" }}></div>
          </div>
        )}
      </div>

      {/* Fullscreen modal */}
      {fullscreen && (
        <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg w-full max-w-5xl max-h-[90vh] flex flex-col">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="font-bold text-lg">{result.title}</h3>
              <button onClick={toggleFullscreen} className="text-gray-500 hover:text-gray-700">
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="flex-1 p-4">
              <div ref={chartRef} className="w-full h-full"></div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
