"use client"

import { lazy, Suspense } from "react"
import { Graduation<PERSON>ap, Bar<PERSON>hart2, Book<PERSON><PERSON> } from "lucide-react"

// Lazy load the ServiceCard and ServicesGrid components
const ServiceCard = lazy(() => 
  import("@/components/ui/service-card").then(module => ({ 
    default: module.ServiceCard 
  }))
)

const ServicesGrid = lazy(() => 
  import("@/components/ui/service-card").then(module => ({ 
    default: module.ServicesGrid 
  }))
)

// Loading skeleton for service cards
function ServiceCardSkeleton() {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
      <div className="flex justify-center mb-6">
        <div className="w-16 h-16 bg-gray-200 rounded-2xl"></div>
      </div>
      <div className="h-6 bg-gray-200 rounded mb-4 mx-auto w-3/4"></div>
      <div className="space-y-2 mb-4">
        <div className="h-4 bg-gray-200 rounded"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        <div className="h-4 bg-gray-200 rounded w-4/6"></div>
      </div>
      <div className="space-y-2 mb-6">
        <div className="h-3 bg-gray-200 rounded w-full"></div>
        <div className="h-3 bg-gray-200 rounded w-5/6"></div>
        <div className="h-3 bg-gray-200 rounded w-4/6"></div>
        <div className="h-3 bg-gray-200 rounded w-3/6"></div>
      </div>
      <div className="h-10 bg-gray-200 rounded"></div>
    </div>
  )
}

function ServicesGridSkeleton() {
  return (
    <section className="py-20 bg-gradient-to-b from-gray-50 to-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="h-12 bg-gray-200 rounded mb-4 mx-auto w-1/2 animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded mx-auto w-2/3 animate-pulse"></div>
          <div className="w-24 h-1 bg-gray-200 mx-auto mt-8 rounded-full animate-pulse"></div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
          <ServiceCardSkeleton />
          <ServiceCardSkeleton />
          <ServiceCardSkeleton />
        </div>
      </div>
    </section>
  )
}

// Main lazy services component
export function LazyServices() {
  return (
    <Suspense fallback={<ServicesGridSkeleton />}>
      <ServicesGrid
        title="Our Services"
        subtitle="Comprehensive statistical solutions backed by decades of experience and expertise"
      >
        <Suspense fallback={<ServiceCardSkeleton />}>
          <ServiceCard
            icon={GraduationCap}
            title="Teaching"
            description="More than 20 years of experience teaching statistics courses at internationally recognized institutions"
            features={[
              "Biostatistics and epidemiology courses",
              "Advanced statistical methods",
              "Research methodology training",
              "International certification programs"
            ]}
            ctaText="Explore Courses"
            ctaHref="/courses"
            iconColor="bg-accent"
            delay={100}
          />
        </Suspense>

        <Suspense fallback={<ServiceCardSkeleton />}>
          <ServiceCard
            icon={BarChart2}
            title="Research"
            description="Privilege of publishing more than 150 research papers in internationally recognized and abstracted journals"
            features={[
              "Peer-reviewed publications",
              "Statistical methodology research",
              "Collaborative research projects",
              "Academic partnerships"
            ]}
            ctaText="View Publications"
            ctaHref="/research"
            iconColor="bg-secondary"
            delay={200}
          />
        </Suspense>

        <Suspense fallback={<ServiceCardSkeleton />}>
          <ServiceCard
            icon={BookOpen}
            title="Consultancy"
            description="More than 15 years experience providing statistical consultancy, automation, KPIs calculation and monitoring"
            features={[
              "Statistical analysis and modeling",
              "Data automation solutions",
              "KPI development and monitoring",
              "Custom statistical software"
            ]}
            ctaText="Get Consultation"
            ctaHref="/statistical-consultancy"
            iconColor="bg-primary"
            delay={300}
          />
        </Suspense>
      </ServicesGrid>
    </Suspense>
  )
}

// Export individual components for direct use
export { ServiceCardSkeleton, ServicesGridSkeleton }
