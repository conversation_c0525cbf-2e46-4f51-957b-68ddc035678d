"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { SearchIcon, X } from "lucide-react"
import { useRouter } from "next/navigation"

// Mock search data - in a real application, this would come from a database or API
const searchData = [
  { title: "Home", path: "/" },
  { title: "Courses", path: "/courses" },
  { title: "Biostatistics Course", path: "/courses/biostatistics" },
  { title: "Survival Analysis Course", path: "/courses/survival-analysis" },
  { title: "Statistical Computing Course", path: "/courses/statistical-computing" },
  { title: "Research", path: "/research" },
  { title: "Publications", path: "/publications" },
  { title: "Resume", path: "/resume" },
  { title: "Statistical Consultancy", path: "/statistical-consultancy" },
  { title: "Survey Administration", path: "/surveys" },
  { title: "Contact", path: "/contact" },
]

export default function Search() {
  const [isOpen, setIsOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [results, setResults] = useState<typeof searchData>([])
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()

  // Handle search query changes
  useEffect(() => {
    if (searchQuery.length > 1) {
      const filteredResults = searchData.filter((item) => item.title.toLowerCase().includes(searchQuery.toLowerCase()))
      setResults(filteredResults)
    } else {
      setResults([])
    }
  }, [searchQuery])

  // Handle click outside to close search
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Focus input when search is opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Open search with Ctrl+K or Cmd+K
      if ((e.ctrlKey || e.metaKey) && e.key === "k") {
        e.preventDefault()
        setIsOpen((prev) => !prev)
      }

      // Close search with Escape
      if (e.key === "Escape") {
        setIsOpen(false)
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => {
      window.removeEventListener("keydown", handleKeyDown)
    }
  }, [])

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (results.length > 0) {
      router.push(results[0].path)
      setIsOpen(false)
      setSearchQuery("")
    }
  }

  const handleResultClick = (path: string) => {
    router.push(path)
    setIsOpen(false)
    setSearchQuery("")
  }

  return (
    <div className="relative" ref={searchRef}>
      <button
        onClick={() => setIsOpen(true)}
        className="p-2 rounded-full hover:bg-gray-100 transition-colors duration-200 flex items-center"
        aria-label="Search"
      >
        <SearchIcon className="h-5 w-5 text-gray-600" />
        <span className="hidden md:inline ml-2 text-sm text-gray-500">Search (Ctrl+K)</span>
      </button>

      {isOpen && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-start justify-center pt-20 px-4">
          <div className="bg-white rounded-lg shadow-2xl w-full max-w-2xl overflow-hidden animate-fadeIn">
            <div className="p-4 border-b">
              <form onSubmit={handleSearchSubmit} className="flex items-center">
                <SearchIcon className="h-5 w-5 text-gray-400 mr-3" />
                <input
                  ref={inputRef}
                  type="text"
                  placeholder="Search for anything..."
                  className="flex-1 outline-none text-gray-700"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <button type="button" onClick={() => setIsOpen(false)} className="p-1 rounded-full hover:bg-gray-100">
                  <X className="h-5 w-5 text-gray-400" />
                </button>
              </form>
            </div>

            {results.length > 0 && (
              <div className="max-h-96 overflow-y-auto p-2">
                <div className="text-xs text-gray-500 px-3 py-2">Results</div>
                <ul>
                  {results.map((result, index) => (
                    <li key={index}>
                      <button
                        onClick={() => handleResultClick(result.path)}
                        className="w-full text-left px-3 py-2 hover:bg-gray-100 rounded flex items-center"
                      >
                        <SearchIcon className="h-4 w-4 text-gray-400 mr-3" />
                        <span>{result.title}</span>
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {searchQuery.length > 1 && results.length === 0 && (
              <div className="p-4 text-center text-gray-500">No results found for "{searchQuery}"</div>
            )}

            {searchQuery.length <= 1 && (
              <div className="p-4 text-center text-gray-500">Type at least 2 characters to search</div>
            )}

            <div className="p-3 bg-gray-50 text-xs text-gray-500 border-t">
              <div className="flex justify-between">
                <span>
                  Press <kbd className="px-2 py-1 bg-gray-100 rounded">↑</kbd>{" "}
                  <kbd className="px-2 py-1 bg-gray-100 rounded">↓</kbd> to navigate
                </span>
                <span>
                  Press <kbd className="px-2 py-1 bg-gray-100 rounded">Enter</kbd> to select
                </span>
                <span>
                  Press <kbd className="px-2 py-1 bg-gray-100 rounded">Esc</kbd> to close
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
