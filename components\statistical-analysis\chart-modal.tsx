"use client"

import { useState } from "react"
import { X, Search } from "lucide-react"
import type { DataSet, ChartType } from "./types"
import { chartRegistry } from "./chart-registry"
import VariableSelector from "./variable-selector"

interface ChartModalProps {
  isOpen: boolean
  onClose: () => void
  onCreateChart: (type: ChartType, variables: string[]) => void
  dataset: DataSet
}

export default function ChartModal({ isOpen, onClose, onCreateChart, dataset }: ChartModalProps) {
  const [selectedChart, setSelectedChart] = useState<ChartType | "">("")
  const [selectedVariables, setSelectedVariables] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")

  // Find the selected chart configuration
  const chartConfig = chartRegistry.find((chart) => chart.type === selectedChart)

  // Filter charts based on search term
  const filteredCharts = chartRegistry.filter((chart) => {
    return (
      chart.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      chart.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })

  const handleChartChange = (type: ChartType | "") => {
    setSelectedChart(type)
    setSelectedVariables([])
  }

  const handleCreateChart = () => {
    if (!selectedChart) return

    onCreateChart(selectedChart, selectedVariables)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] flex flex-col">
        <div className="p-4 border-b flex justify-between items-center">
          <h3 className="font-bold text-lg">Create Chart</h3>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-6 flex-1 overflow-auto">
          {/* Search box for charts */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Chart Type</label>
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search charts..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 pl-9"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            </div>
          </div>

          {/* Chart type selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-6">
            {filteredCharts.map((chart) => (
              <div
                key={chart.type}
                className={`p-3 border rounded-md cursor-pointer transition-colors ${
                  selectedChart === chart.type ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:bg-gray-50"
                }`}
                onClick={() => handleChartChange(chart.type)}
              >
                <h4 className="font-medium">{chart.name}</h4>
                <p className="text-sm text-gray-500 mt-1">{chart.description}</p>
              </div>
            ))}
          </div>

          {/* Variable selection */}
          {selectedChart && chartConfig && (
            <div>
              <div className="mb-2">
                <h4 className="font-medium">Select Variables</h4>
                <p className="text-sm text-gray-500">
                  {chartConfig.minVariables === chartConfig.maxVariables
                    ? `Select exactly ${chartConfig.minVariables} variable(s)`
                    : `Select ${chartConfig.minVariables}-${chartConfig.maxVariables} variable(s)`}
                </p>
              </div>

              <VariableSelector
                dataset={dataset}
                selectedVariables={selectedVariables}
                onChange={setSelectedVariables}
                filterByType={chartConfig.requiredVariableTypes[0]}
                maxSelections={chartConfig.maxVariables}
                multiSelect={chartConfig.maxVariables > 1}
              />
            </div>
          )}
        </div>

        <div className="p-4 border-t flex justify-end">
          <button onClick={onClose} className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded mr-2">
            Cancel
          </button>
          <button
            onClick={handleCreateChart}
            className="bg-[#2196f3] hover:bg-[#1976d2] text-white px-4 py-2 rounded"
            disabled={
              !selectedChart ||
              !chartConfig ||
              selectedVariables.length < chartConfig.minVariables ||
              selectedVariables.length > chartConfig.maxVariables
            }
          >
            Create Chart
          </button>
        </div>
      </div>
    </div>
  )
}
