/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    // Static export requires unoptimized images
    unoptimized: true,
    dangerouslyAllowSVG: false,
  },
  // Enable experimental features for better performance
  experimental: {
    scrollRestoration: true,
    // optimizeCss: true, // Disabled - requires critters dependency
  },
  // Optimize bundle
  webpack: (config) => {
    // Basic optimizations that work with Next.js
    return config;
  },
  // Compress responses
  compress: true,
  // Enable static export for deployment
  output: 'export',
  trailingSlash: true,
}

export default nextConfig
