"use client"

import Image from "next/image"
import { motion } from "framer-motion"
import LinkWrapper from "@/components/link-wrapper"

const courses = [
  {
    id: 1,
    title: "Biostatistics",
    image: "/biostatistics.jpeg",
    description:
      "Introduction to Biostatistics course provides an introduction to selected important and commonly used topics in medical research.",
    link: "/courses/biostatistics",
  },
  {
    id: 2,
    title: "Survival Analysis",
    image: "/SurvivalAnalysis.png",
    description:
      "The main idea of the course is to develop a critical approach to the analysis of survival data often encountered in health and actuarial sciences research",
    link: "/courses/survival-analysis",
  },
  {
    id: 3,
    title: "Quantitative Techniques",
    image: "/quantitative-techniques.png",
    description:
      "It is applied course in statistics that is designed to provide you with the concepts and methods of statistical analysis for decision making under uncertainties",
    link: "/courses/quantitative-techniques",
  },
  {
    id: 4,
    title: "Statistical Computing",
    image: "/r-logo.png",
    description:
      "Learn modern statistical computing techniques using R, Python, and other statistical software for data analysis and visualization.",
    link: "/courses/statistical-computing",
  },
  {
    id: 5,
    title: "Research Methodology",
    image: "/categorical-data-analysis.gif",
    description:
      "Comprehensive course on research design, data collection methods, and analysis techniques for conducting high-quality research.",
    link: "/courses/research-methodology",
  },
  {
    id: 6,
    title: "Advanced Statistical Methods",
    image: "/3d-graph.png",
    description:
      "Explore advanced statistical techniques including multivariate analysis, time series analysis, and machine learning applications.",
    link: "/courses/advanced-statistical-methods",
  },
]

export default function CoursesPage() {
  const cardHover = {
    rest: { scale: 1 },
    hover: {
      scale: 1.03,
      boxShadow: "0 10px 30px rgba(0,0,0,0.1)",
      transition: {
        duration: 0.3,
        ease: "easeInOut",
      },
    },
  }

  return (
    <div className="py-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold">Courses Details</h1>
          <div className="w-24 h-1 bg-[#f5a623] mx-auto mt-4"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {courses.map((course) => (
            <motion.div
              key={course.id}
              variants={cardHover}
              initial="rest"
              whileHover="hover"
              className="bg-white rounded-lg shadow-lg overflow-hidden"
            >
              <div className="relative h-48">
                <Image src={course.image || "/placeholder.svg"} alt={course.title} fill className="object-cover" />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-3">{course.title}</h3>
                <p className="text-gray-600 mb-4">{course.description}</p>
                <LinkWrapper
                  href={`/courses/${
                    course.id === 1
                      ? "biostatistics"
                      : course.id === 2
                        ? "survival-analysis"
                        : course.id === 3
                          ? "quantitative-techniques"
                          : course.id === 4
                            ? "statistical-computing"
                            : course.id === 5
                              ? "research-methodology"
                              : "advanced-statistical-methods"
                  }`}
                  className="inline-block text-[#f5a623] font-semibold hover:underline"
                >
                  Read More
                </LinkWrapper>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  )
}
