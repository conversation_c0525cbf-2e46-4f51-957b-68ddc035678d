"use client"

import { useState } from "react"
import { X, ChevronDown, ChevronUp } from "lucide-react"
import type { AnalysisResult } from "./types"

interface AnalysisResultDisplayProps {
  result: AnalysisResult
  onRemove: (id: string) => void
}

export default function AnalysisResultDisplay({ result, onRemove }: AnalysisResultDisplayProps) {
  const [expanded, setExpanded] = useState(true)

  // Function to organize descriptive statistics into categories
  const organizeDescriptiveStats = () => {
    if (!result.tableData || result.title !== "Descriptive Statistics - Numeric Variables") {
      return null
    }

    // Extract variable names (first column of each row)
    const variables = result.tableData.rows.map((row) => row[0])

    // Create organized data structure
    const organizedData = variables.map((variable, index) => {
      const row = result.tableData!.rows[index]

      return {
        variable,
        centralTendency: {
          n: row[1],
          mean: row[4],
          median: row[5],
          sum: "N/A", // Not in original data
        },
        dispersion: {
          stdDev: row[6],
          variance: row[7],
          min: row[2],
          max: row[3],
          range: "N/A", // Can be calculated
        },
        percentiles: {
          q1: row[8],
          q3: row[9],
          iqr: row[10],
        },
        distribution: {
          skewness: row[11],
          kurtosis: row[12],
        },
      }
    })

    return organizedData
  }

  const organizedStats = organizeDescriptiveStats()

  return (
    <div className="bg-white border rounded-lg shadow-sm mb-4 overflow-hidden">
      <div className="flex justify-between items-center p-4 border-b bg-gray-50">
        <div className="flex items-center">
          <button onClick={() => setExpanded(!expanded)} className="mr-2 text-gray-500 hover:text-gray-700">
            {expanded ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </button>
          <h3 className="font-bold text-lg">{result.title}</h3>
        </div>
        <button
          onClick={() => onRemove(result.id)}
          className="text-gray-400 hover:text-gray-600"
          aria-label="Remove result"
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      {expanded && (
        <div className="p-4">
          {organizedStats ? (
            <div>
              {organizedStats.map((stat, index) => (
                <div key={index} className="mb-6 last:mb-0">
                  <h4 className="font-semibold text-lg mb-3 text-blue-600">{stat.variable}</h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Central Tendency */}
                    <div className="border rounded-md p-3">
                      <h5 className="font-medium mb-2 text-gray-700">Central Tendency</h5>
                      <table className="w-full text-sm">
                        <tbody>
                          <tr>
                            <td className="py-1 pr-2 text-gray-600">N:</td>
                            <td>{stat.centralTendency.n}</td>
                          </tr>
                          <tr>
                            <td className="py-1 pr-2 text-gray-600">Mean:</td>
                            <td>{stat.centralTendency.mean}</td>
                          </tr>
                          <tr>
                            <td className="py-1 pr-2 text-gray-600">Median:</td>
                            <td>{stat.centralTendency.median}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    {/* Dispersion */}
                    <div className="border rounded-md p-3">
                      <h5 className="font-medium mb-2 text-gray-700">Dispersion</h5>
                      <table className="w-full text-sm">
                        <tbody>
                          <tr>
                            <td className="py-1 pr-2 text-gray-600">Std Dev:</td>
                            <td>{stat.dispersion.stdDev}</td>
                          </tr>
                          <tr>
                            <td className="py-1 pr-2 text-gray-600">Variance:</td>
                            <td>{stat.dispersion.variance}</td>
                          </tr>
                          <tr>
                            <td className="py-1 pr-2 text-gray-600">Min:</td>
                            <td>{stat.dispersion.min}</td>
                          </tr>
                          <tr>
                            <td className="py-1 pr-2 text-gray-600">Max:</td>
                            <td>{stat.dispersion.max}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    {/* Percentiles */}
                    <div className="border rounded-md p-3">
                      <h5 className="font-medium mb-2 text-gray-700">Percentile Values</h5>
                      <table className="w-full text-sm">
                        <tbody>
                          <tr>
                            <td className="py-1 pr-2 text-gray-600">Q1 (25%):</td>
                            <td>{stat.percentiles.q1}</td>
                          </tr>
                          <tr>
                            <td className="py-1 pr-2 text-gray-600">Q3 (75%):</td>
                            <td>{stat.percentiles.q3}</td>
                          </tr>
                          <tr>
                            <td className="py-1 pr-2 text-gray-600">IQR:</td>
                            <td>{stat.percentiles.iqr}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    {/* Distribution */}
                    <div className="border rounded-md p-3">
                      <h5 className="font-medium mb-2 text-gray-700">Distribution</h5>
                      <table className="w-full text-sm">
                        <tbody>
                          <tr>
                            <td className="py-1 pr-2 text-gray-600">Skewness:</td>
                            <td>{stat.distribution.skewness}</td>
                          </tr>
                          <tr>
                            <td className="py-1 pr-2 text-gray-600">Kurtosis:</td>
                            <td>{stat.distribution.kurtosis}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : result.title === "Independent Samples T-Test" && result.tableData ? (
            <div>
              <h4 className="font-semibold text-lg mb-3">Group Statistics</h4>
              <div className="overflow-x-auto mb-6">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      {result.tableData.headers.map((header, index) => (
                        <th
                          key={index}
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {header}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {result.tableData.rows.map((row, rowIndex) => (
                      <tr key={rowIndex}>
                        {row.map((cell, cellIndex) => (
                          <td key={cellIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {cell}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {result.additionalData?.tTestTable && (
                <>
                  <h4 className="font-semibold text-lg mb-3">Independent Samples Test</h4>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          {result.additionalData.tTestTable.headers.map((header, index) => (
                            <th
                              key={index}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                            >
                              {header}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {result.additionalData.tTestTable.rows.map((row, rowIndex) => (
                          <tr key={rowIndex}>
                            {row.map((cell, cellIndex) => (
                              <td key={cellIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {cell}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </>
              )}
            </div>
          ) : result.title === "Cross-tabulation Analysis" && result.tableData ? (
            <div>
              <div className="overflow-x-auto mb-6">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      {result.tableData.headers.map((header, index) => (
                        <th
                          key={index}
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {header}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {result.tableData.rows.map((row, rowIndex) => (
                      <tr key={rowIndex}>
                        {row.map((cell, cellIndex) => (
                          <td key={cellIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {cell}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {result.additionalData?.chiSquare && (
                <div className="mt-4 p-4 bg-gray-50 rounded-md">
                  <h4 className="font-semibold mb-2">Chi-Square Test for Independence</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Chi-Square Value</p>
                      <p className="font-medium">{result.additionalData.chiSquare.chiSquare.toFixed(3)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Degrees of Freedom</p>
                      <p className="font-medium">{result.additionalData.chiSquare.df}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">p-value</p>
                      <p className="font-medium">{result.additionalData.chiSquare.pValue.toFixed(3)}</p>
                    </div>
                  </div>
                  <div className="mt-2">
                    <p className="text-sm">
                      <span className="font-medium">Interpretation: </span>
                      {result.additionalData.chiSquare.pValue < 0.05
                        ? "There is a significant association between the variables (p < 0.05)"
                        : "There is no significant association between the variables (p > 0.05)"}
                    </p>
                  </div>
                </div>
              )}
            </div>
          ) : result.tableData ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    {result.tableData.headers.map((header, index) => (
                      <th
                        key={index}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {result.tableData.rows.map((row, rowIndex) => (
                    <tr key={rowIndex}>
                      {row.map((cell, cellIndex) => (
                        <td key={cellIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {cell}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <pre className="whitespace-pre-wrap text-sm">{result.content}</pre>
          )}
        </div>
      )}
    </div>
  )
}
