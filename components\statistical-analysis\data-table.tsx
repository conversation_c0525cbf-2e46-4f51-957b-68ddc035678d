"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON>D<PERSON>, Arrow<PERSON><PERSON>Down } from "lucide-react"
import type { DataSet, SortConfig } from "./types"

interface DataTableProps {
  dataset: DataSet
  sortConfig: SortConfig | null
  onSort: (column: string, direction: "asc" | "desc") => void
}

export default function DataTable({ dataset, sortConfig, onSort }: DataTableProps) {
  const [page, setPage] = useState(0)
  const rowsPerPage = 10

  // Calculate total pages
  const totalPages = Math.ceil(dataset.rows.length / rowsPerPage)

  // Get current page of data
  const currentRows = dataset.rows.slice(page * rowsPerPage, (page + 1) * rowsPerPage)

  // Handle sort
  const handleSortClick = (column: string) => {
    const direction = sortConfig?.column === column && sortConfig?.direction === "asc" ? "desc" : "asc"
    onSort(column, direction)
  }

  return (
    <div className="border rounded overflow-auto bg-white h-full">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50 sticky top-0">
          <tr>
            {dataset.headers.map((header, index) => (
              <th
                key={index}
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap"
              >
                <div className="flex items-center">
                  <span className="mr-1">{header}</span>
                  <button
                    onClick={() => handleSortClick(header)}
                    className="text-gray-400 hover:text-gray-600"
                    aria-label={`Sort by ${header}`}
                  >
                    {sortConfig?.column === header ? (
                      sortConfig.direction === "asc" ? (
                        <ArrowUp className="h-3 w-3" />
                      ) : (
                        <ArrowDown className="h-3 w-3" />
                      )
                    ) : (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </button>
                  {dataset.columnTypes && (
                    <span className="ml-1 text-xs px-1 py-0.5 rounded bg-gray-200">
                      {dataset.columnTypes[header]}
                    </span>
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {currentRows.map((row, rowIndex) => (
            <tr key={rowIndex} className={rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50"}>
              {dataset.headers.map((header, colIndex) => (
                <td
                  key={colIndex}
                  className="px-6 py-4 whitespace-nowrap text-sm text-gray-800"
                  contentEditable={true}
                  onBlur={(e) => {
                    const updatedRows = [...dataset.rows];
                    updatedRows[page * rowsPerPage + rowIndex][header] = e.currentTarget.textContent || "";
                    // In a real application, you would likely want to update the state in the parent component
                    // For this example, we'll just log the change
                    console.log(`Edited cell at row ${page * rowsPerPage + rowIndex}, column ${header}: ${e.currentTarget.textContent}`);
                  }}
                >
                  {row[header]?.toString() || ""}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="bg-gray-50 px-6 py-3 flex items-center justify-between border-t border-gray-200 sticky bottom-0">
          <div className="text-sm text-gray-500">
            Showing {page * rowsPerPage + 1} to {Math.min((page + 1) * rowsPerPage, dataset.rows.length)} of{" "}
            {dataset.rows.length} rows
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setPage(Math.max(0, page - 1))}
              disabled={page === 0}
              className="px-3 py-1 border rounded text-sm disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => setPage(Math.min(totalPages - 1, page + 1))}
              disabled={page === totalPages - 1}
              className="px-3 py-1 border rounded text-sm disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
