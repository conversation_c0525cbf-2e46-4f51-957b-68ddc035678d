import { v4 as uuidv4 } from "uuid";
import * as simpleStatistics from "simple-statistics"; // Keep for other potential uses
import { jStat } from "jstat";
import type { AnalysisType, DataSet, AnalysisResult, NumericStats } from "./types";

// Helper functions for statistical calculations
const calculateNumericStats = (data: number[]): NumericStats => {
  // Sort data for percentiles
  const sortedData = [...data].sort((a, b) => a - b)
  const n = data.length

  // Basic statistics
  const min = Math.min(...data)
  const max = Math.max(...data)
  const range = max - min
  const sum = data.reduce((acc, val) => acc + val, 0)
  const mean = sum / n

  // Median and quartiles
  const median = n % 2 === 0 ? (sortedData[n / 2 - 1] + sortedData[n / 2]) / 2 : sortedData[Math.floor(n / 2)]

  const q1Index = Math.floor(n * 0.25)
  const q3Index = Math.floor(n * 0.75)
  const q1 = sortedData[q1Index]
  const q3 = sortedData[q3Index]
  const iqr = q3 - q1

  // Variance and standard deviation
  const squaredDiffs = data.map((val) => Math.pow(val - mean, 2))
  const variance = squaredDiffs.reduce((acc, val) => acc + val, 0) / n
  const stdDev = Math.sqrt(variance)

  // Standard error of mean
  const stdError = stdDev / Math.sqrt(n)

  // Skewness and kurtosis
  const cubedDiffs = data.map((val) => Math.pow((val - mean) / stdDev, 3))
  const fourthDiffs = data.map((val) => Math.pow((val - mean) / stdDev, 4))

  const skewness = n > 0 ? cubedDiffs.reduce((acc, val) => acc + val, 0) / n : 0
  const kurtosis = n > 0 ? fourthDiffs.reduce((acc, val) => acc + val, 0) / n - 3 : 0

  // Simple approximation of Shapiro-Wilk test p-value
  const shapiroWilk = {
    statistic: 0.95, // Placeholder
    pValue: skewness < 0.5 && kurtosis < 0.5 ? 0.8 : 0.01, // Very simplified approximation
  }

  return {
    n,
    min,
    max,
    range,
    mean,
    median,
    q1,
    q3,
    iqr,
    variance,
    stdDev,
    stdError,
    skewness,
    kurtosis,
    normality: {
      shapiroWilk,
    },
  }
}

// Calculate frequency distribution for categorical variables
const calculateFrequencyDistribution = (
  data: (string | number)[],
): Record<string, { count: number; percentage: number }> => {
  const frequencies: Record<string, { count: number; percentage: number }> = {}
  const total = data.length

  // Count occurrences of each value
  data.forEach((value) => {
    const strValue = String(value)
    if (!frequencies[strValue]) {
      frequencies[strValue] = { count: 1, percentage: 0 }
    } else {
      frequencies[strValue].count++
    }
  })

  // Calculate percentages
  Object.keys(frequencies).forEach((key) => {
    frequencies[key].percentage = (frequencies[key].count / total) * 100
  })

  return frequencies
}

// Calculate Chi-Square test for independence
const calculateChiSquare = (
  observed: number[][],
  rowTotals: number[],
  colTotals: number[],
  grandTotal: number,
): { chiSquare: number; df: number; pValue: number } => {
  let chiSquare = 0
  const rows = observed.length
  const cols = observed[0].length

  // Calculate chi-square statistic
  for (let i = 0; i < rows; i++) {
    for (let j = 0; j < cols; j++) {
      // Calculate expected value
      const expected = (rowTotals[i] * colTotals[j]) / grandTotal

      // Add to chi-square if expected is not zero
      if (expected !== 0) {
        chiSquare += Math.pow(observed[i][j] - expected, 2) / expected
      }
    }
  }

  // Calculate degrees of freedom
  const df = (rows - 1) * (cols - 1)

  // Calculate p-value using jStat
  const pValue = df > 0 ? jStat.chisquare.pval(chiSquare, df) : 1; // Avoid calculation if df is 0 or less

  return { chiSquare, df, pValue };
};

// Calculate t-test for independent samples (Equal Variances Assumed ONLY)
const calculateIndependentTTest = (
  group1: number[],
  group2: number[],
): {
  t: number;
  df: number;
  pValue: number;
} => {
  // Calculate statistics for each group
  const n1 = group1.length
  const n2 = group2.length

  const mean1 = group1.reduce((sum, val) => sum + val, 0) / n1
  const mean2 = group2.reduce((sum, val) => sum + val, 0) / n2

  const squaredDiffs1 = group1.map((val) => Math.pow(val - mean1, 2))
  const squaredDiffs2 = group2.map((val) => Math.pow(val - mean2, 2))

  const variance1 = squaredDiffs1.reduce((sum, val) => sum + val, 0) / (n1 - 1)
  const variance2 = squaredDiffs2.reduce((sum, val) => sum + val, 0) / (n2 - 1)

  // Equal variance t-test calculations
  const pooledVariance = ((n1 - 1) * variance1 + (n2 - 1) * variance2) / (n1 + n2 - 2)
  const standardError = Math.sqrt(pooledVariance * (1 / n1 + 1 / n2))
  const t = (mean1 - mean2) / standardError
  const df = n1 + n2 - 2

  // Calculate p-value for equal variances using jStat's t-distribution CDF
  // Check if df is valid (positive finite number) and t is finite
  const pValue = (df && isFinite(df) && df > 0 && isFinite(t))
      ? 2 * (1 - jStat.studentt.cdf(Math.abs(t), df))
      : 1.0; // Default to 1.0 if df or t is invalid

  return {
    t,
    df,
    pValue,
  };
};

// Analysis registry
export const analysisRegistry = [
  {
    type: "descriptive-numeric" as AnalysisType,
    name: "Descriptive Statistics - Numeric Variables",
    description: "Calculate basic descriptive statistics for numeric variables (mean, median, std dev, etc.)",
    requiredVariableTypes: ["numeric"] as const,
    minVariables: 1,
    maxVariables: 10,
    needsGroupingVariable: false,
    execute: (dataset: DataSet, variables: string[]): AnalysisResult => {
      // Filter to only include selected numeric variables
      const numericVariables = variables.filter((variable) => dataset.columnTypes?.[variable] === "numeric")

      if (numericVariables.length === 0) {
        return {
          id: uuidv4(),
          title: "Descriptive Statistics - Numeric Variables",
          content: "No numeric variables selected for analysis.",
        }
      }

      // Calculate statistics for each numeric variable
      const statsResults: Record<string, NumericStats> = {}

      numericVariables.forEach((variable) => {
        // Extract numeric values for this variable
        const values = dataset.rows.map((row) => Number(row[variable])).filter((val) => !isNaN(val))

        if (values.length > 0) {
          statsResults[variable] = calculateNumericStats(values)
        }
      })

      // Create table data for display
      const tableHeaders = [
        "Variable",
        "N",
        "Min",
        "Max",
        "Mean",
        "Median",
        "Std Dev",
        "Variance",
        "Q1",
        "Q3",
        "IQR",
        "Skewness",
        "Kurtosis",
      ]

      const tableRows = Object.entries(statsResults).map(([variable, stats]) => [
        variable,
        stats.n,
        stats.min.toFixed(2),
        stats.max.toFixed(2),
        stats.mean.toFixed(2),
        stats.median.toFixed(2),
        stats.stdDev.toFixed(2),
        stats.variance.toFixed(2),
        stats.q1.toFixed(2),
        stats.q3.toFixed(2),
        stats.iqr.toFixed(2),
        stats.skewness.toFixed(2),
        stats.kurtosis.toFixed(2),
      ])

      // Create detailed text report
      let content = `Descriptive Statistics for Numeric Variables\n\n`

      Object.entries(statsResults).forEach(([variable, stats]) => {
        content += `Variable: ${variable}\n`
        content += `N: ${stats.n}\n`
        content += `Min: ${stats.min.toFixed(2)}\n`
        content += `Max: ${stats.max.toFixed(2)}\n`
        content += `Range: ${stats.range.toFixed(2)}\n`
        content += `Mean: ${stats.mean.toFixed(2)}\n`
        content += `Median: ${stats.median.toFixed(2)}\n`
        content += `Standard Deviation: ${stats.stdDev.toFixed(2)}\n`
        content += `Variance: ${stats.variance.toFixed(2)}\n`
        content += `Q1 (25th percentile): ${stats.q1.toFixed(2)}\n`
        content += `Q3 (75th percentile): ${stats.q3.toFixed(2)}\n`
        content += `IQR: ${stats.iqr.toFixed(2)}\n`
        content += `Skewness: ${stats.skewness.toFixed(2)}\n`
        content += `Kurtosis: ${stats.kurtosis.toFixed(2)}\n`
        // Handle potentially undefined pValue before calling toFixed
        const shapiroPValue = stats.normality.shapiroWilk?.pValue;
        content += `Normality (Shapiro-Wilk p-value): ${shapiroPValue !== undefined ? shapiroPValue.toFixed(3) : 'N/A'}\n`
        content += `Interpretation: ${
          shapiroPValue !== undefined && shapiroPValue > 0.05
            ? "Data appears normally distributed (based on approximation)"
            : "Data may not be normally distributed (based on approximation)"
        }\n\n`
      })

      return {
        id: uuidv4(),
        title: "Descriptive Statistics - Numeric Variables",
        content: content,
        tableData: {
          headers: tableHeaders,
          rows: tableRows,
        },
      }
    },
  },
  {
    type: "descriptive-categorical" as AnalysisType,
    name: "Descriptive Statistics - Categorical Variables",
    description: "Calculate frequency distributions for categorical variables",
    requiredVariableTypes: ["categorical"] as const,
    minVariables: 1,
    maxVariables: 10,
    needsGroupingVariable: false,
    execute: (dataset: DataSet, variables: string[]): AnalysisResult => {
      // Filter to only include selected categorical variables
      const categoricalVariables = variables.filter((variable) => dataset.columnTypes?.[variable] === "categorical")

      if (categoricalVariables.length === 0) {
        return {
          id: uuidv4(),
          title: "Descriptive Statistics - Categorical Variables",
          content: "No categorical variables selected for analysis.",
        }
      }

      // Calculate frequency distribution for each categorical variable
      const frequencyResults: Record<string, Record<string, { count: number; percentage: number }>> = {}

      categoricalVariables.forEach((variable) => {
        // Extract values for this variable
        const values = dataset.rows.map((row) => row[variable])
        frequencyResults[variable] = calculateFrequencyDistribution(values)
      })

      // Create detailed text report
      let content = `Frequency Distribution for Categorical Variables\n\n`

      // Create table data for display
      const allTableData: {
        variable: string
        headers: string[]
        rows: any[][]
      }[] = []

      Object.entries(frequencyResults).forEach(([variable, frequencies]) => {
        content += `Variable: ${variable}\n`
        content += `Total observations: ${dataset.rows.length}\n\n`
        content += `Category\tCount\tPercentage\n`

        const tableHeaders = ["Category", "Count", "Percentage (%)"]
        const tableRows: any[][] = []

        Object.entries(frequencies)
          .sort((a, b) => b[1].count - a[1].count)
          .forEach(([category, { count, percentage }]) => {
            content += `${category}\t${count}\t${percentage.toFixed(2)}%\n`
            tableRows.push([category, count, percentage.toFixed(2)])
          })

        content += "\n"
        allTableData.push({
          variable,
          headers: tableHeaders,
          rows: tableRows,
        })
      })

      return {
        id: uuidv4(),
        title: "Descriptive Statistics - Categorical Variables",
        content: content,
        tableData: {
          headers: ["Variable", "Category", "Count", "Percentage (%)"],
          rows: allTableData.flatMap((data) => data.rows.map((row) => [data.variable, ...row])),
        },
      }
    },
  },
  {
    type: "crosstab" as AnalysisType,
    name: "Cross-tabulation",
    description: "Create a contingency table between two categorical variables with Chi-Square test",
    requiredVariableTypes: ["categorical"] as const,
    minVariables: 1,
    maxVariables: 1,
    needsGroupingVariable: true,
    execute: (dataset: DataSet, variables: string[], groupingVariable?: string): AnalysisResult => {
      if (!groupingVariable || variables.length === 0) {
        return {
          id: uuidv4(),
          title: "Cross-tabulation Analysis",
          content: "Insufficient variables selected for cross-tabulation.",
        }
      }

      // We need at least one variable and one grouping variable
      const targetVariable = variables[0]

      // Get unique values for both variables
      const targetValues = Array.from(new Set(dataset.rows.map((row) => String(row[targetVariable]))))
      const groupValues = Array.from(new Set(dataset.rows.map((row) => String(row[groupingVariable]))))

      // Create cross-tabulation table
      const crosstab: Record<string, Record<string, number>> = {}
      const rowTotals: Record<string, number> = {}
      const colTotals: Record<string, number> = {}
      let grandTotal = 0

      // Initialize crosstab
      targetValues.forEach((targetVal) => {
        crosstab[targetVal] = {}
        rowTotals[targetVal] = 0

        groupValues.forEach((groupVal) => {
          crosstab[targetVal][groupVal] = 0
          if (!colTotals[groupVal]) colTotals[groupVal] = 0
        })
      })

      // Fill crosstab with counts
      dataset.rows.forEach((row) => {
        const targetVal = String(row[targetVariable])
        const groupVal = String(row[groupingVariable])

        if (crosstab[targetVal] && groupValues.includes(groupVal)) {
          crosstab[targetVal][groupVal]++
          rowTotals[targetVal]++
          colTotals[groupVal]++
          grandTotal++
        }
      })

      // Create observed values matrix for chi-square calculation
      const observedValues = targetValues.map((targetVal) =>
        groupValues.map((groupVal) => crosstab[targetVal][groupVal]),
      )

      // Calculate Chi-Square test
      const chiSquareResult = calculateChiSquare(
        observedValues,
        Object.values(rowTotals),
        Object.values(colTotals),
        grandTotal,
      )

      // Create table data for display
      const tableHeaders = ["", ...groupValues, "Total"]
      const tableRows = targetValues.map((targetVal) => [
        targetVal,
        ...groupValues.map((groupVal) => crosstab[targetVal][groupVal]),
        rowTotals[targetVal],
      ])

      // Add totals row
      tableRows.push(["Total", ...groupValues.map((groupVal) => colTotals[groupVal]), grandTotal])

      // Create detailed text report
      let content = `Cross-tabulation: ${targetVariable} by ${groupingVariable}\n\n`

      // Format as text table
      content += `${targetVariable}\\${groupingVariable}\t${groupValues.join("\t")}\tTotal\n`

      targetValues.forEach((targetVal) => {
        content += `${targetVal}\t${groupValues.map((groupVal) => crosstab[targetVal][groupVal]).join("\t")}\t${
          rowTotals[targetVal]
        }\n`
      })

      content += `Total\t${groupValues.map((groupVal) => colTotals[groupVal]).join("\t")}\t${grandTotal}\n\n`

      // Add percentages
      content += "Percentages (Row):\n\n"
      content += `${targetVariable}\\${groupingVariable}\t${groupValues.join("\t")}\tTotal\n`

      targetValues.forEach((targetVal) => {
        content += `${targetVal}\t${groupValues
          .map((groupVal) =>
            rowTotals[targetVal] > 0
              ? ((crosstab[targetVal][groupVal] / rowTotals[targetVal]) * 100).toFixed(1) + "%"
              : "0.0%",
          )
          .join("\t")}\t100.0%\n`
      })

      // Add Chi-Square test results
      content += "\nChi-Square Test for Independence:\n\n"
      content += `Chi-Square value: ${chiSquareResult.chiSquare.toFixed(3)}\n`
      content += `Degrees of freedom: ${chiSquareResult.df}\n`
      content += `p-value: ${chiSquareResult.pValue.toFixed(3)}\n`
      content += `Interpretation: ${
        chiSquareResult.pValue < 0.05
          ? "There is a significant association between the variables (p < 0.05)"
          : "There is no significant association between the variables (p > 0.05)"
      }\n`

      // Create a second table with row percentages
      const percentageTableRows = targetValues.map((targetVal) => [
        targetVal,
        ...groupValues.map((groupVal) =>
          rowTotals[targetVal] > 0
            ? `${crosstab[targetVal][groupVal]} (${((crosstab[targetVal][groupVal] / rowTotals[targetVal]) * 100).toFixed(1)}%)`
            : "0 (0.0%)",
        ),
        rowTotals[targetVal],
      ])

      // Add totals row to percentage table
      percentageTableRows.push([
        "Total",
        ...groupValues.map(
          (groupVal) => `${colTotals[groupVal]} (${((colTotals[groupVal] / grandTotal) * 100).toFixed(1)}%)`,
        ),
        grandTotal,
      ])

      return {
        id: uuidv4(),
        title: "Cross-tabulation Analysis",
        content: content,
        tableData: {
          headers: tableHeaders,
          rows: percentageTableRows,
        },
        additionalData: {
          chiSquare: chiSquareResult,
          rawCounts: tableRows,
        },
      }
    },
  },
  {
    type: "ttest-independent" as AnalysisType,
    name: "Independent Samples T-Test",
    description: "Compare means between two groups (Equal Variances Assumed)", // Updated description
    requiredVariableTypes: ["numeric"] as const,
    minVariables: 1,
    maxVariables: 1,
    needsGroupingVariable: true,
    execute: (dataset: DataSet, variables: string[], groupingVariable?: string): AnalysisResult => {
      if (!groupingVariable || variables.length === 0) {
        return {
          id: uuidv4(),
          title: "Independent Samples T-Test",
          content: "Insufficient variables selected for t-test.",
        }
      }

      const numericVariable = variables[0]

      // Get unique values for grouping variable
      const groupValues = Array.from(new Set(dataset.rows.map((row) => String(row[groupingVariable]))))

      // We need exactly two groups for independent t-test
      if (groupValues.length !== 2) {
        return {
          id: uuidv4(),
          title: "Independent Samples T-Test",
          content: "The grouping variable must have exactly two categories for an independent samples t-test.",
        }
      }

      // Extract values for each group
      const group1Values: number[] = []
      const group2Values: number[] = []

      dataset.rows.forEach((row) => {
        const value = Number(row[numericVariable])
        if (!isNaN(value)) {
          if (String(row[groupingVariable]) === groupValues[0]) {
            group1Values.push(value)
          } else if (String(row[groupingVariable]) === groupValues[1]) {
            group2Values.push(value)
          }
        }
      })

      // Calculate descriptive statistics for each group
      const group1Stats = calculateNumericStats(group1Values)
      const group2Stats = calculateNumericStats(group2Values)

      // Calculate t-test (Equal Variances Assumed only)
      const tTestResults = calculateIndependentTTest(group1Values, group2Values)

      // Create table data for display - Group Statistics
      const groupStatsHeaders = ["", "Group", "N", "Mean", "Std. Deviation", "Std. Error Mean"]
      const groupStatsRows = [
        [
          numericVariable,
          groupValues[0],
          group1Stats.n,
          group1Stats.mean.toFixed(4),
          group1Stats.stdDev.toFixed(5),
          group1Stats.stdError.toFixed(5),
        ],
        [
          "",
          groupValues[1],
          group2Stats.n,
          group2Stats.mean.toFixed(4),
          group2Stats.stdDev.toFixed(5),
          group2Stats.stdError.toFixed(5),
        ],
      ]

      // Create table data for t-test results (Only Equal Variances Assumed)
      const tTestHeaders = ["", "t", "df", "Sig. (2-tailed)"]
      const tTestRows = [
        [
          numericVariable,
          tTestResults.t.toFixed(3),
          tTestResults.df,
          tTestResults.pValue.toFixed(3),
        ],
      ]

      // Create detailed text report
      let content = `Independent Samples T-Test: ${numericVariable} by ${groupingVariable}\n\n`

      content += "Group Statistics:\n\n"
      content += `Variable: ${numericVariable}\n`
      content += `Group: ${groupValues[0]}\n`
      content += `N: ${group1Stats.n}\n`
      content += `Mean: ${group1Stats.mean.toFixed(4)}\n`
      content += `Std. Deviation: ${group1Stats.stdDev.toFixed(5)}\n`
      content += `Std. Error Mean: ${group1Stats.stdError.toFixed(5)}\n\n`

      content += `Group: ${groupValues[1]}\n`
      content += `N: ${group2Stats.n}\n`
      content += `Mean: ${group2Stats.mean.toFixed(4)}\n`
      content += `Std. Deviation: ${group2Stats.stdDev.toFixed(5)}\n`
      content += `Std. Error Mean: ${group2Stats.stdError.toFixed(5)}\n\n`

      content += "Independent Samples Test (Equal Variances Assumed):\n\n" // Updated title
      content += `t: ${tTestResults.t.toFixed(3)}\n`
      content += `df: ${tTestResults.df}\n`
      content += `Sig. (2-tailed): ${tTestResults.pValue.toFixed(3)}\n\n`

      // Interpretation based on the standard t-test
      content += "Interpretation (Assuming Equal Variances):\n";
      if (tTestResults.pValue < 0.05) {
          content += "There is a significant difference between the means of the two groups (p < 0.05).\n";
      } else {
          content += "There is no significant difference between the means of the two groups (p >= 0.05).\n";
      }


      return {
        id: uuidv4(),
        title: "Independent Samples T-Test",
        content: content,
        tableData: {
          headers: groupStatsHeaders,
          rows: groupStatsRows,
        },
        additionalData: {
          tTestTable: {
            headers: tTestHeaders,
            rows: tTestRows,
          },
        },
      }
    },
  },
  // Add more analyses here
]

// Helper function to get analysis by type
export function getAnalysisByType(type: AnalysisType) {
  return analysisRegistry.find((analysis) => analysis.type === type)
}
