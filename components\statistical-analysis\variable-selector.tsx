"use client"

import { useState } from "react"
import { Search } from "lucide-react"
import type { DataSet } from "./types"

interface VariableSelectorProps {
  dataset: DataSet
  selectedVariables: string[]
  onToggleVariable?: (variable: string) => void
  onChange?: (variables: string[]) => void
  filterType?: "numeric" | "categorical"
  maxSelections?: number
  label?: string
  multiSelect?: boolean
  filterByType?: "numeric" | "categorical"
}

export default function VariableSelector({
  dataset,
  selectedVariables,
  onToggleVariable,
  onChange,
  filterType,
  maxSelections = Number.POSITIVE_INFINITY,
  label,
  multiSelect = true,
  filterByType,
}: VariableSelectorProps) {
  const [searchTerm, setSearchTerm] = useState("")

  // Filter variables based on type and search term
  const filteredVariables = dataset.headers.filter((header) => {
    const matchesType = filterType ? dataset.columnTypes?.[header] === filterType : true
    const matchesSearch = header.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesType && matchesSearch
  })

  const handleToggle = (variable: string) => {
    if (onToggleVariable) {
      onToggleVariable(variable)
    } else if (onChange) {
      if (selectedVariables.includes(variable)) {
        onChange(selectedVariables.filter((v) => v !== variable))
      } else {
        if (!multiSelect) {
          onChange([variable])
        } else if (selectedVariables.length < maxSelections) {
          onChange([...selectedVariables, variable])
        }
      }
    }
  }

  return (
    <div className="mb-4">
      {label && <div className="font-medium mb-2">{label}</div>}

      <div className="relative mb-2">
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Search variables..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 pl-9"
        />
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
      </div>

      <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-md">
        {filteredVariables.length > 0 ? (
          filteredVariables.map((variable) => (
            <div
              key={variable}
              className={`flex items-center p-2 hover:bg-gray-50 cursor-pointer ${
                selectedVariables.includes(variable) ? "bg-blue-50" : ""
              }`}
              onClick={() => handleToggle(variable)}
            >
              <input
                type={multiSelect ? "checkbox" : "radio"}
                checked={selectedVariables.includes(variable)}
                onChange={() => {}}
                className="mr-2"
              />
              <span>{variable}</span>
              <span className="ml-2 text-xs text-gray-500">({dataset.columnTypes?.[variable] || "unknown"})</span>
            </div>
          ))
        ) : (
          <div className="p-3 text-gray-500 text-center">No matching variables found</div>
        )}
      </div>

      {maxSelections < Number.POSITIVE_INFINITY && (
        <div className="mt-1 text-xs text-gray-500">
          {selectedVariables.length}/{maxSelections} variables selected
        </div>
      )}
    </div>
  )
}
