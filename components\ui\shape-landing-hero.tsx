"use client"

import { Circle } from "lucide-react"
import { cn } from "@/lib/utils"
import Image from "next/image"

function ElegantShape({
  className,
  width = 400,
  height = 100,
  rotate = 0,
  gradient = "from-white/[0.08]",
  delay = 0,
}: {
  className?: string
  width?: number
  height?: number
  rotate?: number
  gradient?: string
  delay?: number
}) {
  return (
    <div
      className={cn("absolute animate-float", className)}
      style={{
        transform: `rotate(${rotate}deg)`,
        animationDelay: `${delay}s`,
      }}
    >
      <div
        style={{ width, height }}
        className="relative"
      >
        <div
          className={cn(
            "absolute inset-0 rounded-full",
            "bg-gradient-to-r to-transparent",
            gradient,
            "backdrop-blur-[2px] border-2 border-white/[0.15]",
            "shadow-[0_8px_32px_0_rgba(255,255,255,0.1)]",
            "after:absolute after:inset-0 after:rounded-full",
            "after:bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.2),transparent_70%)]",
          )}
        />
      </div>
    </div>
  )
}

function HeroGeometric({
  badge = "Design Collective",
  title1 = "Elevate Your Digital Vision",
  title2 = "Crafting Exceptional Websites",
}: {
  badge?: string
  title1?: string
  title2?: string
}) {

  return (
    <div className="relative min-h-screen w-full flex items-center justify-center overflow-hidden bg-gradient-to-r from-[#0a2158] to-[#1a3a7a]">
      <div className="absolute inset-0 bg-gradient-to-br from-[#0a2158]/[0.2] via-transparent to-[#1a3a7a]/[0.2] blur-3xl" />

      {/* Optimized background with CSS instead of external image */}
      <div className="absolute inset-0 bg-hero-pattern opacity-30" />

      {/* Reduced number of shapes for better performance */}
      <div className="absolute inset-0 overflow-hidden z-0">
        <ElegantShape
          width={600}
          height={140}
          rotate={12}
          gradient="from-[#4ecdc4]/[0.15]"
          className="left-[-10%] md:left-[-5%] top-[15%] md:top-[20%]"
          delay={0.5}
        />

        <ElegantShape
          width={500}
          height={120}
          rotate={-15}
          gradient="from-[#0a2158]/[0.15]"
          className="right-[-5%] md:right-[0%] top-[70%] md:top-[75%]"
          delay={1.0}
        />

        <ElegantShape
          width={200}
          height={60}
          rotate={20}
          gradient="from-[#f5a623]/[0.15]"
          className="right-[15%] md:right-[20%] top-[10%] md:top-[15%]"
          delay={1.5}
        />
      </div>

      <div className="relative z-10 container mx-auto px-4 md:px-6">
        <div className="max-w-3xl mx-auto text-center">
          <div className="animate-hero-fade-up">
            <h1 className="text-3xl sm:text-5xl md:text-6xl font-bold mb-6 md:mb-8 tracking-tight">
              <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">{title1}</span>
              <br />
              <span
                className={cn(
                  "bg-clip-text text-transparent bg-gradient-to-r from-[#4ecdc4] via-white/90 to-[#f5a623] ",
                )}
              >
                {title2}
              </span>
            </h1>
          </div>

          <div className="animate-hero-fade-up" style={{ animationDelay: '0.2s' }}>
            <p className="text-base sm:text-lg md:text-xl text-white/70 mb-8 leading-relaxed font-light tracking-wide max-w-xl mx-auto px-4">
              {/* Content for description if needed */}
            </p>
          </div>

          <div
            className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/[0.1] border border-white/[0.15] mb-8 md:mb-12 animate-hero-fade-up"
            style={{ animationDelay: '0.4s' }}
          >
            <Circle className="h-3 w-3 fill-[#f5a623]" />
            <span className="text-base md:text-lg text-white/90 tracking-wide font-medium">{badge}</span>
          </div>
        </div>
      </div>

      <div className="absolute inset-0 bg-gradient-to-t from-[#0a2158]/80 via-transparent to-[#0a2158]/30 pointer-events-none" />
    </div>
  )
}

export { HeroGeometric }
