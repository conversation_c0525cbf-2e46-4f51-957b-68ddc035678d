"use client"

import Image from "next/image"
import { useState } from "react"

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  fill?: boolean
  className?: string
  priority?: boolean
  sizes?: string
  quality?: number
  placeholder?: "blur" | "empty"
  blurDataURL?: string
}

export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  fill,
  className,
  priority = false,
  sizes,
  quality = 85,
  placeholder = "empty",
  blurDataURL,
  ...props
}: OptimizedImageProps) {
  const [imageError, setImageError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Check if we have an optimized version available
  const getOptimizedSrc = (originalSrc: string) => {
    // If it's an external URL, return as-is
    if (originalSrc.startsWith('http')) {
      return originalSrc
    }

    // Remove leading slash if present
    const cleanSrc = originalSrc.startsWith('/') ? originalSrc.slice(1) : originalSrc
    const filename = cleanSrc.split('/').pop()
    
    if (!filename) return originalSrc

    const name = filename.split('.')[0]
    const optimizedImages = [
      'statistical-computing',
      'survival-analysis', 
      'workshops',
      'survey',
      'nsb-award',
      'biostatistics',
      'online-survey'
    ]

    // If this is one of our optimized images, use the WebP version
    if (optimizedImages.includes(name)) {
      return `/optimized/${name}.webp`
    }

    return originalSrc
  }

  const optimizedSrc = getOptimizedSrc(src)

  // Generate responsive sizes if not provided
  const responsiveSizes = sizes || (
    fill 
      ? "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      : width && width > 800 
        ? "(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px"
        : undefined
  )

  return (
    <div className={`relative ${isLoading ? 'animate-pulse bg-gray-200' : ''}`}>
      <Image
        src={imageError ? src : optimizedSrc} // Fallback to original if optimized fails
        alt={alt}
        width={width}
        height={height}
        fill={fill}
        className={className}
        priority={priority}
        sizes={responsiveSizes}
        quality={quality}
        placeholder={placeholder}
        blurDataURL={blurDataURL}

        onLoad={() => setIsLoading(false)}
        onError={() => {
          setImageError(true)
          setIsLoading(false)
        }}
        {...props}
      />
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded" />
      )}
    </div>
  )
}
