import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import Header from "@/components/header"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import WebVitals, { PerformanceMonitor } from "@/components/web-vitals"

const inter = Inter({
  subsets: ["latin"],
  display: 'swap',
  preload: true,
  fallback: ['system-ui', 'arial']
})

export const metadata: Metadata = {
  title: "N<PERSON><PERSON><PERSON> - Professor of Biostatistics | Statistical Consultancy",
  description:
    "Professional statistical consultancy services for students, researchers, and health professionals. We provide end-to-end survey solutions, statistical software training, and dashboard management.",
  keywords: [
    "biostatistics",
    "statistical consultancy",
    "research methodology",
    "survey administration",
    "statistical software training",
    "data analysis",
    "clinical trials",
    "survival analysis",
    "dashboard management",
    "health statistics",
  ],
  authors: [{ name: "Professor <PERSON><PERSON><PERSON>" }],
  creator: "Professor <PERSON><PERSON><PERSON>",
  publisher: "NSBSTAT",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://nsbstat.com",
    title: "NSBSTAT - Statistical Consultancy & Research Services",
    description:
      "Professional statistical consultancy for researchers, students, and health professionals. Specializing in biostatistics, survey administration, and statistical training.",
    siteName: "NSBSTAT",
  },
  twitter: {
    card: "summary_large_image",
    title: "NSBSTAT - Statistical Consultancy & Research Services",
    description: "Professional statistical consultancy for researchers, students, and health professionals.",
    creator: "@nsbstat",
  },
  alternates: {
    canonical: "https://nsbstat.com",
  },
  other: {
    "google-site-verification": "your-verification-code",
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        {/* Security headers */}
        <meta httpEquiv="Content-Security-Policy" content="frame-ancestors 'self'; frame-src 'self' https://www.google.com https://maps.google.com https://form.jotform.com;" />
        <meta httpEquiv="X-Frame-Options" content="SAMEORIGIN" />
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        {/* Preload critical resources */}
        <link rel="preload" href="/logo.png" as="image" type="image/png" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="https://hebbkx1anhila5yf.public.blob.vercel-storage.com" />
      </head>
      <body className={inter.className}>
        <WebVitals />
        <PerformanceMonitor />
        <Header />
        <Navbar />
        <main>{children}</main>
        <Footer />
      </body>
    </html>
  )
}
