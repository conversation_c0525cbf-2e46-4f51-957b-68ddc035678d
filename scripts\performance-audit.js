const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs');
const path = require('path');

async function runLighthouse(url, options = {}) {
  const chrome = await chromeLauncher.launch({
    chromeFlags: ['--headless', '--no-sandbox', '--disable-dev-shm-usage']
  });
  
  const runnerResult = await lighthouse(url, {
    ...options,
    port: chrome.port,
  });

  await chrome.kill();
  return runnerResult;
}

async function performanceAudit() {
  console.log('🚀 Starting performance audit...');
  
  const url = process.env.AUDIT_URL || 'http://localhost:3000';
  const outputDir = path.join(__dirname, '../performance-reports');
  
  // Create output directory if it doesn't exist
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  try {
    console.log(`📊 Auditing: ${url}`);
    
    const runnerResult = await runLighthouse(url, {
      onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
      output: 'html',
      disableDeviceEmulation: false,
      throttlingMethod: 'simulate',
    });

    // Save HTML report
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = path.join(outputDir, `lighthouse-report-${timestamp}.html`);
    fs.writeFileSync(reportPath, runnerResult.report);
    
    // Extract key metrics
    const lhr = runnerResult.lhr;
    const metrics = {
      performance: lhr.categories.performance.score * 100,
      accessibility: lhr.categories.accessibility.score * 100,
      bestPractices: lhr.categories['best-practices'].score * 100,
      seo: lhr.categories.seo.score * 100,
      fcp: lhr.audits['first-contentful-paint'].numericValue,
      lcp: lhr.audits['largest-contentful-paint'].numericValue,
      cls: lhr.audits['cumulative-layout-shift'].numericValue,
      fid: lhr.audits['max-potential-fid']?.numericValue || 0,
      ttfb: lhr.audits['server-response-time']?.numericValue || 0,
    };
    
    // Save metrics as JSON
    const metricsPath = path.join(outputDir, `metrics-${timestamp}.json`);
    fs.writeFileSync(metricsPath, JSON.stringify(metrics, null, 2));
    
    // Console output
    console.log('\n📈 Performance Metrics:');
    console.log(`Performance Score: ${metrics.performance.toFixed(1)}/100`);
    console.log(`Accessibility Score: ${metrics.accessibility.toFixed(1)}/100`);
    console.log(`Best Practices Score: ${metrics.bestPractices.toFixed(1)}/100`);
    console.log(`SEO Score: ${metrics.seo.toFixed(1)}/100`);
    console.log('\n⚡ Core Web Vitals:');
    console.log(`First Contentful Paint: ${(metrics.fcp / 1000).toFixed(2)}s`);
    console.log(`Largest Contentful Paint: ${(metrics.lcp / 1000).toFixed(2)}s`);
    console.log(`Cumulative Layout Shift: ${metrics.cls.toFixed(3)}`);
    console.log(`Time to First Byte: ${(metrics.ttfb / 1000).toFixed(2)}s`);
    
    console.log(`\n📄 Full report saved to: ${reportPath}`);
    console.log(`📊 Metrics saved to: ${metricsPath}`);
    
    // Performance recommendations
    console.log('\n💡 Performance Recommendations:');
    if (metrics.performance < 90) {
      console.log('- Consider further optimizing images and JavaScript bundles');
    }
    if (metrics.lcp > 2500) {
      console.log('- Largest Contentful Paint is slow - optimize critical resources');
    }
    if (metrics.cls > 0.1) {
      console.log('- Cumulative Layout Shift is high - ensure proper image dimensions');
    }
    if (metrics.fcp > 1800) {
      console.log('- First Contentful Paint is slow - optimize critical rendering path');
    }
    
  } catch (error) {
    console.error('❌ Error running performance audit:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  performanceAudit();
}

module.exports = { performanceAudit, runLighthouse };
