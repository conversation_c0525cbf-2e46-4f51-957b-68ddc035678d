import Link from "next/link"
import { Facebook, Twitter, Youtube, Linkedin, Mail, Phone } from "lucide-react"

export default function Header() {
  return (
    <div className="bg-gradient-to-r from-[#0a2158] to-[#1a3a7a] text-white py-3">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="text-center md:text-left">
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Nadeem Shafique Butt</h1>
            <div className="flex justify-center md:justify-start mt-1 space-x-2">
              <Link
                href="https://www.facebook.com/nadeem.shafique.pk/"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white/10 hover:bg-white/20 p-1.5 rounded-full transition-all duration-300 transform hover:scale-110"
              >
                <Facebook size={16} />
                <span className="sr-only">Facebook</span>
              </Link>
              <Link
                href="https://x.com/nadeemshafique"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white/10 hover:bg-white/20 p-1.5 rounded-full transition-all duration-300 transform hover:scale-110"
              >
                <Twitter size={16} />
                <span className="sr-only">Twitter</span>
              </Link>
              <Link
                href="https://www.youtube.com/nadeemshafique"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white/10 hover:bg-white/20 p-1.5 rounded-full transition-all duration-300 transform hover:scale-110"
              >
                <Youtube size={16} />
                <span className="sr-only">Youtube</span>
              </Link>
              <Link
                href="https://www.linkedin.com/in/nadeem-shafique-butt-99a41418/"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white/10 hover:bg-white/20 p-1.5 rounded-full transition-all duration-300 transform hover:scale-110"
              >
                <Linkedin size={16} />
                <span className="sr-only">LinkedIn</span>
              </Link>
            </div>
          </div>

          <div className="text-center md:text-right flex flex-col md:flex-row items-center md:items-end gap-2 md:gap-4 mt-2 md:mt-0">
            <div>
              <h2 className="text-[#4ecdc4] font-bold text-sm md:text-base">Professor of Biostatistics</h2>
              <p className="text-white/90 text-xs md:text-sm">Department of Family and Community Medicine</p>
              <p className="text-white/90 text-xs md:text-sm">King Abdulaziz University, KSA</p>
            </div>
            <div className="flex flex-col md:items-end gap-1">
              <div className="flex items-center text-white/90 text-xs md:text-sm">
                <Mail className="h-3 w-3 mr-1" />
                <a href="mailto:<EMAIL>" className="text-[#4ecdc4]">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center text-white/90 text-xs md:text-sm">
                <Phone className="h-3 w-3 mr-1" />
                <a href="tel:+92-312-4441234" className="text-[#4ecdc4]">
                  +92-312-4441234
                </a>
              </div>
            </div>
            <Link
              href="/contact"
              className="inline-block px-4 py-1 bg-[#f5a623] text-white text-sm rounded-full hover:bg-[#e09612] transition-colors duration-300 shadow-md"
            >
              Contact
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
