"use client"

import { useEffect } from 'react'

// Web Vitals monitoring component
export default function WebVitals() {
  useEffect(() => {
    // Only run in production
    if (process.env.NODE_ENV !== 'production') return

    // Dynamic import to avoid bundling in development
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      // Core Web Vitals
      getCLS(sendToAnalytics)
      getFID(sendToAnalytics)
      getFCP(sendToAnalytics)
      getLCP(sendToAnalytics)
      getTTFB(sendToAnalytics)
    }).catch(error => {
      console.warn('Failed to load web-vitals:', error)
    })
  }, [])

  return null
}

function sendToAnalytics(metric: any) {
  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log('Web Vital:', metric)
    return
  }

  // Send to your analytics service
  // Example: Google Analytics 4
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', metric.name, {
      custom_parameter_1: metric.value,
      custom_parameter_2: metric.id,
      custom_parameter_3: metric.name,
    })
  }

  // For static export, use console logging or external analytics
  if (typeof window !== 'undefined') {
    // Option 1: Console logging for development
    if (process.env.NODE_ENV === 'development') {
      console.log('Web Vital:', {
        name: metric.name,
        value: metric.value,
        id: metric.id,
        timestamp: Date.now(),
        url: window.location.href,
      })
    }

    // Option 2: Send to external analytics service (Google Analytics, etc.)
    // Replace with your preferred analytics service
    try {
      // Example: Google Analytics 4
      if (typeof gtag !== 'undefined') {
        gtag('event', metric.name, {
          value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
          event_category: 'Web Vitals',
          event_label: metric.id,
          non_interaction: true,
        })
      }
    } catch (error) {
      console.warn('Failed to send analytics:', error)
    }
  }
}

// Performance observer for additional metrics
export function PerformanceMonitor() {
  useEffect(() => {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return
    }

    // Add error boundary for performance monitoring
    const handleError = (error: any) => {
      if (process.env.NODE_ENV === 'development') {
        console.warn('Performance monitoring error:', error)
      }
    }

    // Monitor long tasks
    try {
      const longTaskObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) {
            console.warn('Long task detected:', {
              duration: entry.duration,
              startTime: entry.startTime,
            })
          }
        }
      })
      longTaskObserver.observe({ entryTypes: ['longtask'] })

      // Monitor layout shifts
      const layoutShiftObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if ((entry as any).hadRecentInput) continue
          // Only log significant layout shifts in development
          if (process.env.NODE_ENV === 'development' && (entry as any).value > 0.1) {
            console.log('Layout shift:', {
              value: (entry as any).value,
              startTime: entry.startTime,
            })
          }
        }
      })
      layoutShiftObserver.observe({ entryTypes: ['layout-shift'] })

      // Cleanup
      return () => {
        try {
          longTaskObserver.disconnect()
          layoutShiftObserver.disconnect()
        } catch (error) {
          handleError(error)
        }
      }
    } catch (error) {
      handleError(error)
    }
  }, [])

  return null
}
