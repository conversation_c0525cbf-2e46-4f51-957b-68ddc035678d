"use client"

import Image from "next/image"
import Link from "next/link"
import { Briefcase, GraduationCap, Award, BookOpen, Users, Globe } from "lucide-react"
import { motion } from "framer-motion"

export default function ResumePage() {
  const cardHover = {
    rest: { scale: 1 },
    hover: {
      scale: 1.02,
      boxShadow: "0 10px 30px rgba(0,0,0,0.1)",
      transition: {
        duration: 0.3,
        ease: "easeInOut",
      },
    },
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  }

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  return (
    <div className="py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <motion.div
            variants={cardHover}
            initial="rest"
            whileHover="hover"
            className="bg-white rounded-lg shadow-lg overflow-hidden mb-8"
          >
            <div className="bg-gradient-to-r from-[#0a2158] to-[#1a3a7a] text-white p-6 md:p-8">
              <div className="flex flex-col md:flex-row items-center">
                <div className="mb-4 md:mb-0 md:mr-6">
                  <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-white">
                    <Image
                      src="/profile-photo.jpeg"
                      alt="Professor Nadeem Shafique Butt"
                      width={128}
                      height={128}
                      className="object-cover"
                    />
                  </div>
                </div>
                <div className="text-center md:text-left">
                  <h1 className="text-2xl md:text-3xl font-bold mb-2">Nadeem Shafique Butt</h1>
                  <p className="text-xl text-[#4ecdc4] font-semibold mb-2">Professor of Biostatistics</p>
                  <p className="mb-1">Department of Family and Community Medicine</p>
                  <p>King Abdulaziz University, Kingdom of Saudi Arabia</p>
                </div>
              </div>
            </div>
            <div className="p-6 md:p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h2 className="text-lg font-bold mb-3">Contact Information</h2>
                  <ul className="space-y-2">
                    <li className="flex items-center">
                      <span className="font-semibold mr-2">Email:</span>
                      <a href="mailto:<EMAIL>" className="text-[#4ecdc4] hover:underline">
                        <EMAIL>
                      </a>
                    </li>
                    <li className="flex items-center">
                      <span className="font-semibold mr-2">Phone:</span>
                      <a href="tel:+92-312-4441234" className="text-[#4ecdc4] hover:underline">
                        +92-312-4441234
                      </a>
                    </li>
                    <li className="flex items-center">
                      <span className="font-semibold mr-2">Website:</span>
                      <a href="https://nsbstat.com" className="text-[#4ecdc4] hover:underline">
                        nsbstat.com
                      </a>
                    </li>
                  </ul>
                </div>
                <div>
                  <h2 className="text-lg font-bold mb-3">Research Interests</h2>
                  <ul className="list-disc list-inside space-y-1 text-gray-700">
                    <li>Biostatistics</li>
                    <li>Survival Analysis</li>
                    <li>Clinical Trials</li>
                    <li>Statistical Computing</li>
                    <li>Medical Research Methodology</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Education */}
          <motion.div
            variants={cardHover}
            initial="rest"
            whileHover="hover"
            className="bg-white rounded-lg shadow-lg overflow-hidden mb-8"
          >
            <div className="bg-[#0a2158] text-white p-4 flex items-center">
              <GraduationCap className="h-6 w-6 mr-2" />
              <h2 className="text-xl font-bold">Education</h2>
            </div>
            <div className="p-6">
              <motion.div
                variants={staggerContainer}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                className="space-y-6"
              >
                <motion.div variants={fadeInUp} custom={0} className="mb-6">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-bold">Ph.D. in Statistics</h3>
                    <span className="text-sm bg-gray-100 px-2 py-1 rounded">2005-2009</span>
                  </div>
                  <p className="text-[#4ecdc4] font-semibold mb-1">University of Example, Country</p>
                  <p className="text-gray-700">
                    Dissertation: "Advanced Statistical Methods for Survival Analysis in Medical Research"
                  </p>
                </motion.div>

                <motion.div variants={fadeInUp} custom={1} className="mb-6">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-bold">M.Phil. in Statistics</h3>
                    <span className="text-sm bg-gray-100 px-2 py-1 rounded">2003-2005</span>
                  </div>
                  <p className="text-[#4ecdc4] font-semibold mb-1">University of Example, Country</p>
                  <p className="text-gray-700">
                    Thesis: "Statistical Analysis of Clinical Trial Data: Methodological Considerations"
                  </p>
                </motion.div>

                <motion.div variants={fadeInUp} custom={2}>
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-bold">M.Sc. in Statistics</h3>
                    <span className="text-sm bg-gray-100 px-2 py-1 rounded">2001-2003</span>
                  </div>
                  <p className="text-[#4ecdc4] font-semibold mb-1">University of Example, Country</p>
                  <p className="text-gray-700">Specialized in Biostatistics and Research Methodology</p>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>

          {/* Work Experience */}
          <motion.div
            variants={cardHover}
            initial="rest"
            whileHover="hover"
            className="bg-white rounded-lg shadow-lg overflow-hidden mb-8"
          >
            <div className="bg-[#0a2158] text-white p-4 flex items-center">
              <Briefcase className="h-6 w-6 mr-2" />
              <h2 className="text-xl font-bold">Professional Experience</h2>
            </div>
            <div className="p-6">
              <motion.div
                variants={staggerContainer}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                className="space-y-6"
              >
                <motion.div variants={fadeInUp} custom={0} className="mb-6">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-bold">Professor of Biostatistics</h3>
                    <span className="text-sm bg-gray-100 px-2 py-1 rounded">2018-Present</span>
                  </div>
                  <p className="text-[#4ecdc4] font-semibold mb-1">King Abdulaziz University, Saudi Arabia</p>
                  <p className="text-gray-700 mb-2">Department of Family and Community Medicine</p>
                  <ul className="list-disc list-inside space-y-1 text-gray-700">
                    <li>Teaching graduate and undergraduate courses in biostatistics and research methodology</li>
                    <li>Conducting research in statistical methods for medical and health sciences</li>
                    <li>Providing statistical consultancy for research projects and clinical trials</li>
                    <li>Supervising graduate students' research work</li>
                  </ul>
                </motion.div>

                <motion.div variants={fadeInUp} custom={1} className="mb-6">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-bold">Associate Professor</h3>
                    <span className="text-sm bg-gray-100 px-2 py-1 rounded">2013-2018</span>
                  </div>
                  <p className="text-[#4ecdc4] font-semibold mb-1">University of Example, Country</p>
                  <p className="text-gray-700 mb-2">Department of Statistics</p>
                  <ul className="list-disc list-inside space-y-1 text-gray-700">
                    <li>Taught courses in biostatistics, survival analysis, and research methodology</li>
                    <li>Led research projects in statistical methods for medical research</li>
                    <li>Collaborated with medical researchers on various clinical studies</li>
                  </ul>
                </motion.div>

                <motion.div variants={fadeInUp} custom={2}>
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-bold">Assistant Professor</h3>
                    <span className="text-sm bg-gray-100 px-2 py-1 rounded">2009-2013</span>
                  </div>
                  <p className="text-[#4ecdc4] font-semibold mb-1">University of Example, Country</p>
                  <p className="text-gray-700 mb-2">Department of Statistics</p>
                  <ul className="list-disc list-inside space-y-1 text-gray-700">
                    <li>Taught undergraduate courses in statistics and research methods</li>
                    <li>Conducted research in statistical methods for medical data analysis</li>
                    <li>Provided statistical consultancy for faculty research projects</li>
                  </ul>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>

          {/* Publications */}
          <motion.div
            variants={cardHover}
            initial="rest"
            whileHover="hover"
            className="bg-white rounded-lg shadow-lg overflow-hidden mb-8"
          >
            <div className="bg-[#0a2158] text-white p-4 flex items-center">
              <BookOpen className="h-6 w-6 mr-2" />
              <h2 className="text-xl font-bold">Selected Publications</h2>
            </div>
            <div className="p-6">
              <p className="mb-4 text-gray-700">
                Author of over 150 research papers in internationally recognized and abstracted journals. Selected
                publications include:
              </p>

              <motion.div
                variants={staggerContainer}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                className="space-y-4"
              >
                <motion.div variants={fadeInUp} custom={0} className="border-l-4 border-[#4ecdc4] pl-4">
                  <p className="font-semibold mb-1">Statistical Analysis of COVID-19 Data: Trends and Patterns</p>
                  <p className="text-sm text-gray-600">Journal of Medical Statistics, 2022</p>
                </motion.div>

                <motion.div variants={fadeInUp} custom={1} className="border-l-4 border-[#4ecdc4] pl-4">
                  <p className="font-semibold mb-1">
                    Comparative Study of Survival Analysis Methods in Cancer Research
                  </p>
                  <p className="text-sm text-gray-600">International Journal of Biostatistics, 2021</p>
                </motion.div>

                <motion.div variants={fadeInUp} custom={2} className="border-l-4 border-[#4ecdc4] pl-4">
                  <p className="font-semibold mb-1">Application of Machine Learning in Predicting Disease Outcomes</p>
                  <p className="text-sm text-gray-600">Computational Statistics in Medicine, 2021</p>
                </motion.div>

                <motion.div variants={fadeInUp} custom={3} className="border-l-4 border-[#4ecdc4] pl-4">
                  <p className="font-semibold mb-1">Meta-analysis of Clinical Trials: Methodological Considerations</p>
                  <p className="text-sm text-gray-600">Statistical Methods in Medical Research, 2020</p>
                </motion.div>

                <motion.div variants={fadeInUp} custom={4} className="border-l-4 border-[#4ecdc4] pl-4">
                  <p className="font-semibold mb-1">Bayesian Approaches to Handling Missing Data in Health Surveys</p>
                  <p className="text-sm text-gray-600">Journal of Applied Statistics, 2020</p>
                </motion.div>
              </motion.div>

              <div className="mt-6">
                <Link href="/publications" className="text-[#f5a623] hover:underline font-semibold">
                  View all publications →
                </Link>
              </div>
            </div>
          </motion.div>

          {/* Skills */}
          <motion.div
            variants={cardHover}
            initial="rest"
            whileHover="hover"
            className="bg-white rounded-lg shadow-lg overflow-hidden mb-8"
          >
            <div className="bg-[#0a2158] text-white p-4 flex items-center">
              <Award className="h-6 w-6 mr-2" />
              <h2 className="text-xl font-bold">Skills & Expertise</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                >
                  <h3 className="text-lg font-bold mb-3">Statistical Methods</h3>
                  <ul className="list-disc list-inside space-y-1 text-gray-700">
                    <li>Survival Analysis</li>
                    <li>Clinical Trial Design and Analysis</li>
                    <li>Multivariate Analysis</li>
                    <li>Bayesian Statistics</li>
                    <li>Meta-Analysis</li>
                    <li>Longitudinal Data Analysis</li>
                    <li>Missing Data Methods</li>
                  </ul>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <h3 className="text-lg font-bold mb-3">Software & Tools</h3>
                  <ul className="list-disc list-inside space-y-1 text-gray-700">
                    <li>R Statistical Software</li>
                    <li>SPSS</li>
                    <li>SAS</li>
                    <li>Stata</li>
                    <li>Python for Data Analysis</li>
                    <li>MATLAB</li>
                    <li>REDCap</li>
                  </ul>
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* Professional Memberships */}
          <motion.div
            variants={cardHover}
            initial="rest"
            whileHover="hover"
            className="bg-white rounded-lg shadow-lg overflow-hidden mb-8"
          >
            <div className="bg-[#0a2158] text-white p-4 flex items-center">
              <Users className="h-6 w-6 mr-2" />
              <h2 className="text-xl font-bold">Professional Memberships</h2>
            </div>
            <div className="p-6">
              <motion.ul
                variants={staggerContainer}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                className="space-y-3"
              >
                <motion.li variants={fadeInUp} custom={0} className="flex items-start">
                  <div className="h-6 w-6 rounded-full bg-[#4ecdc4] flex items-center justify-center text-white font-bold mr-3 mt-0.5">
                    1
                  </div>
                  <div>
                    <p className="font-semibold">American Statistical Association (ASA)</p>
                    <p className="text-sm text-gray-600">Member since 2010</p>
                  </div>
                </motion.li>

                <motion.li variants={fadeInUp} custom={1} className="flex items-start">
                  <div className="h-6 w-6 rounded-full bg-[#4ecdc4] flex items-center justify-center text-white font-bold mr-3 mt-0.5">
                    2
                  </div>
                  <div>
                    <p className="font-semibold">International Biometric Society (IBS)</p>
                    <p className="text-sm text-gray-600">Member since 2012</p>
                  </div>
                </motion.li>

                <motion.li variants={fadeInUp} custom={2} className="flex items-start">
                  <div className="h-6 w-6 rounded-full bg-[#4ecdc4] flex items-center justify-center text-white font-bold mr-3 mt-0.5">
                    3
                  </div>
                  <div>
                    <p className="font-semibold">Royal Statistical Society (RSS)</p>
                    <p className="text-sm text-gray-600">Member since 2015</p>
                  </div>
                </motion.li>

                <motion.li variants={fadeInUp} custom={3} className="flex items-start">
                  <div className="h-6 w-6 rounded-full bg-[#4ecdc4] flex items-center justify-center text-white font-bold mr-3 mt-0.5">
                    4
                  </div>
                  <div>
                    <p className="font-semibold">International Society for Clinical Biostatistics (ISCB)</p>
                    <p className="text-sm text-gray-600">Member since 2014</p>
                  </div>
                </motion.li>
              </motion.ul>
            </div>
          </motion.div>

          {/* Languages */}
          <motion.div
            variants={cardHover}
            initial="rest"
            whileHover="hover"
            className="bg-white rounded-lg shadow-lg overflow-hidden"
          >
            <div className="bg-[#0a2158] text-white p-4 flex items-center">
              <Globe className="h-6 w-6 mr-2" />
              <h2 className="text-xl font-bold">Languages</h2>
            </div>
            <div className="p-6">
              <motion.div
                variants={staggerContainer}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                className="grid grid-cols-1 md:grid-cols-3 gap-4"
              >
                <motion.div variants={fadeInUp} custom={0} className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-bold mb-2">English</h3>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div className="bg-[#4ecdc4] h-2.5 rounded-full" style={{ width: "95%" }}></div>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">Professional Proficiency</p>
                </motion.div>

                <motion.div variants={fadeInUp} custom={1} className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-bold mb-2">Arabic</h3>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div className="bg-[#4ecdc4] h-2.5 rounded-full" style={{ width: "80%" }}></div>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">Professional Proficiency</p>
                </motion.div>

                <motion.div variants={fadeInUp} custom={2} className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-bold mb-2">Urdu</h3>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div className="bg-[#4ecdc4] h-2.5 rounded-full" style={{ width: "100%" }}></div>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">Native Proficiency</p>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
