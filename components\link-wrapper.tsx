"use client"

import type React from "react"

import Link from "next/link"
import { useRouter } from "next/navigation"
import type { ReactNode } from "react"

interface LinkWrapperProps {
  href: string
  children: ReactNode
  className?: string
  onClick?: () => void
  style?: React.CSSProperties
}

export default function LinkWrapper({ href, children, className, onClick, style }: LinkWrapperProps) {
  const router = useRouter()

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault()

    // Scroll to top before navigation
    window.scrollTo(0, 0)

    // Execute any additional onClick handler if provided
    if (onClick) {
      onClick()
    }

    // Navigate to the new page
    router.push(href)
  }

  return (
    <Link href={href} className={className} onClick={handleClick} style={style}>
      {children}
    </Link>
  )
}
