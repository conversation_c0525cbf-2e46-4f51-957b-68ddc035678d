import type { Metadata } from "next"

// This is a dynamic metadata generator for course pages
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const slug = params.slug

  // Map slugs to proper titles and descriptions
  const courseInfo: Record<string, { title: string; description: string }> = {
    biostatistics: {
      title: "Biostatistics Course | NSBSTAT",
      description:
        "Introduction to Biostatistics course covering statistical methods and applications in medical research, taught by Professor <PERSON><PERSON><PERSON>.",
    },
    "survival-analysis": {
      title: "Survival Analysis Course | NSBSTAT",
      description:
        "Comprehensive course on survival analysis methods for health and actuarial sciences research, covering <PERSON><PERSON><PERSON><PERSON>, <PERSON> models, and more.",
    },
    "quantitative-techniques": {
      title: "Quantitative Techniques Course | NSBSTAT",
      description:
        "Applied statistics course providing concepts and methods for statistical analysis and decision making under uncertainties.",
    },
    "statistical-computing": {
      title: "Statistical Computing Course | NSBSTAT",
      description:
        "Learn modern statistical computing techniques using R, Python, and other statistical software for data analysis and visualization.",
    },
    "research-methodology": {
      title: "Research Methodology Course | NSBSTAT",
      description:
        "Comprehensive course on research design, data collection methods, and analysis techniques for conducting high-quality research.",
    },
    "advanced-statistical-methods": {
      title: "Advanced Statistical Methods Course | NSBSTAT",
      description:
        "Advanced course covering multivariate analysis, time series analysis, and machine learning applications in statistics.",
    },
  }

  // Default values if slug not found
  const info = courseInfo[slug] || {
    title: "Statistical Course | NSBSTAT",
    description: "Professional statistical course offered by Professor Nadeem Shafique Butt.",
  }

  return {
    title: info.title,
    description: info.description,
    keywords: ["statistics course", "biostatistics", "statistical education", "research methods", slug],
    openGraph: {
      title: info.title,
      description: info.description,
      url: `https://nsbstat.com/courses/${slug}`,
      type: "website",
    },
  }
}
