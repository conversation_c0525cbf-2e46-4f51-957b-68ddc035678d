"use client"
import { motion } from "framer-motion"
import StatisticalAnalysisApp from "@/components/statistical-analysis-app"

export default function StatisticalAnalysisPage() {
  return (
    <div className="py-8">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <section className="mb-12 text-center">
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl font-bold mb-4"
          >
            Statistical Analysis Web Application
          </motion.h1>
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: "96px" }}
            transition={{ delay: 0.3, duration: 0.6 }}
            className="w-24 h-1 bg-[#f5a623] mx-auto mb-6"
          ></motion.div>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.6 }}
            className="text-gray-600 max-w-3xl mx-auto mb-8"
          >
            Perform statistical analyses and create visualizations with our interactive web application. Import your
            data, select from various analysis methods, and generate insightful charts to better understand your data.
          </motion.p>
        </section>

        {/* App Section */}
        <section className="mb-12">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <StatisticalAnalysisApp />
          </div>
        </section>

        {/* Features Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-6 text-center">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="bg-white p-6 rounded-lg shadow-md"
            >
              <h3 className="text-xl font-bold mb-3 text-[#0a2158]">Data Import</h3>
              <p className="text-gray-600">
                Easily import your data from Excel or CSV files. The application automatically detects column types and
                formats your data for analysis.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="bg-white p-6 rounded-lg shadow-md"
            >
              <h3 className="text-xl font-bold mb-3 text-[#0a2158]">Statistical Analyses</h3>
              <p className="text-gray-600">
                Choose from a variety of statistical methods including descriptive statistics, t-tests, ANOVA,
                correlation analysis, and more.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4, duration: 0.5 }}
              className="bg-white p-6 rounded-lg shadow-md"
            >
              <h3 className="text-xl font-bold mb-3 text-[#0a2158]">Data Visualization</h3>
              <p className="text-gray-600">
                Create professional charts and graphs including bar charts, pie charts, histograms, box plots, and
                scatter plots to visualize your data.
              </p>
            </motion.div>
          </div>
        </section>

        {/* How to Use Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-6 text-center">How to Use</h2>
          <div className="bg-white p-8 rounded-lg shadow-md">
            <ol className="space-y-4">
              <li className="flex items-start">
                <span className="bg-[#0a2158] text-white w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-0.5">
                  1
                </span>
                <div>
                  <p className="font-semibold">Import Data</p>
                  <p className="text-gray-600">
                    Click the "Import" button and select your Excel or CSV file. Your data will be loaded into the
                    application.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <span className="bg-[#0a2158] text-white w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-0.5">
                  2
                </span>
                <div>
                  <p className="font-semibold">Select Analysis</p>
                  <p className="text-gray-600">
                    Click the "Analyses" button and choose the statistical method you want to apply to your data.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <span className="bg-[#0a2158] text-white w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-0.5">
                  3
                </span>
                <div>
                  <p className="font-semibold">View Results</p>
                  <p className="text-gray-600">
                    The results of your analysis will appear in the right panel. You can review the statistics and
                    interpretations.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <span className="bg-[#0a2158] text-white w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-0.5">
                  4
                </span>
                <div>
                  <p className="font-semibold">Create Charts</p>
                  <p className="text-gray-600">
                    Click the "Charts" button to visualize your data. Select the chart type that best represents your
                    data.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <span className="bg-[#0a2158] text-white w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-0.5">
                  5
                </span>
                <div>
                  <p className="font-semibold">Save Project</p>
                  <p className="text-gray-600">
                    Click "Save Project" to download your analysis results and charts for future reference.
                  </p>
                </div>
              </li>
            </ol>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-gradient-to-r from-[#0a2158] to-[#1a3a7a] text-white p-8 rounded-lg shadow-lg text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to Analyze Your Data?</h2>
          <p className="mb-6 max-w-2xl mx-auto">
            Start using our Statistical Analysis Web Application today to gain valuable insights from your data.
          </p>
          <button className="bg-[#f5a623] hover:bg-[#e09612] text-white font-bold py-3 px-8 rounded-full transition duration-300">
            Get Started
          </button>
        </section>
      </div>
    </div>
  )
}
