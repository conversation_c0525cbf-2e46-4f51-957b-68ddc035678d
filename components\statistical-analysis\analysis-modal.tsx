"use client"

import type React from "react"

import { useState } from "react"
import { X, Search } from "lucide-react"
import type { DataSet, AnalysisType } from "./types"
import { analysisRegistry } from "./analysis-registry"
import VariableSelector from "./variable-selector"

interface AnalysisModalProps {
  isOpen: boolean
  onClose: () => void
  onRunAnalysis: (type: AnalysisType, variables: string[], groupingVariable?: string) => void
  dataset: DataSet
}

export default function AnalysisModal({ isOpen, onClose, onRunAnalysis, dataset }: AnalysisModalProps) {
  const [selectedAnalysis, setSelectedAnalysis] = useState<AnalysisType | "">("")
  const [selectedVariables, setSelectedVariables] = useState<string[]>([])
  const [groupingVariable, setGroupingVariable] = useState<string>("")
  const [searchTerm, setSearchTerm] = useState("")

  // Find the selected analysis configuration
  const analysisConfig = analysisRegistry.find((analysis) => analysis.type === selectedAnalysis)

  // Filter analyses based on search term
  const filteredAnalyses = analysisRegistry.filter((analysis) => {
    return (
      analysis.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      analysis.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })

  const handleAnalysisChange = (type: AnalysisType | "") => {
    setSelectedAnalysis(type)
    setSelectedVariables([])
    setGroupingVariable("")
  }

  const handleVariableChange = (variables: string[]) => {
    setSelectedVariables(variables)
  }

  const handleGroupingVariableChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setGroupingVariable(e.target.value)
  }

  const handleRunAnalysis = () => {
    if (!selectedAnalysis) return

    onRunAnalysis(selectedAnalysis, selectedVariables, groupingVariable || undefined)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] flex flex-col">
        <div className="p-4 border-b flex justify-between items-center">
          <h3 className="font-bold text-lg">Run Analysis</h3>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="flex-1 overflow-auto p-6">
          {/* Search box for analyses */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Analysis Type</label>
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search analyses..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 pl-9"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            </div>
          </div>

          {/* Analysis type selection */}
          <div className="grid grid-cols-1 gap-2 mb-6">
            {filteredAnalyses.map((analysis) => (
              <div
                key={analysis.type}
                className={`p-3 border rounded-md cursor-pointer transition-colors ${
                  selectedAnalysis === analysis.type ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:bg-gray-50"
                }`}
                onClick={() => handleAnalysisChange(analysis.type)}
              >
                <h4 className="font-medium">{analysis.name}</h4>
                <p className="text-sm text-gray-500 mt-1">{analysis.description}</p>
              </div>
            ))}
          </div>

          {/* Variable selection */}
          {selectedAnalysis && analysisConfig && (
            <>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">Select Variables</label>
                <p className="text-sm text-gray-500 mb-2">
                  {`Select ${
                    analysisConfig.minVariables === analysisConfig.maxVariables
                      ? `exactly ${analysisConfig.minVariables}`
                      : `${analysisConfig.minVariables}-${analysisConfig.maxVariables}`
                  } variable(s)`}
                </p>

                <VariableSelector
                  dataset={dataset}
                  selectedVariables={selectedVariables}
                  onChange={handleVariableChange}
                  filterType={analysisConfig.requiredVariableTypes[0]}
                  maxSelections={analysisConfig.maxVariables}
                  multiSelect={analysisConfig.maxVariables > 1}
                />
              </div>

              {analysisConfig.needsGroupingVariable && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Grouping Variable</label>
                  <p className="text-sm text-gray-500 mb-2">Select a categorical variable to group by</p>
                  <select
                    value={groupingVariable}
                    onChange={handleGroupingVariableChange}
                    className="block w-full border border-gray-300 rounded px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">-- Select Grouping Variable --</option>
                    {dataset.headers
                      .filter((header) => dataset.columnTypes?.[header] === "categorical")
                      .map((header) => (
                        <option key={header} value={header}>
                          {header}
                        </option>
                      ))}
                  </select>
                </div>
              )}
            </>
          )}
        </div>

        <div className="p-4 border-t flex justify-end">
          <button onClick={onClose} className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded mr-2">
            Cancel
          </button>
          <button
            onClick={handleRunAnalysis}
            className="bg-[#2196f3] hover:bg-[#1976d2] text-white px-4 py-2 rounded"
            disabled={
              !selectedAnalysis ||
              !analysisConfig ||
              selectedVariables.length < analysisConfig.minVariables ||
              selectedVariables.length > analysisConfig.maxVariables ||
              (analysisConfig.needsGroupingVariable && !groupingVariable)
            }
          >
            Run Analysis
          </button>
        </div>
      </div>
    </div>
  )
}
