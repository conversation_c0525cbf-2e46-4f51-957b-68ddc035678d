"use client"

import { useEffect, useRef, useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON><PERSON>ounter, <PERSON>zyContactForm, LazyLoadOnScroll } from "@/components/lazy-components"
import { HeroGeometric } from "@/components/ui/shape-landing-hero"
import LinkWrapper from "@/components/link-wrapper"
import OptimizedImage from "@/components/optimized-image"
import { CardHover } from "@/components/motion-wrapper"
import { LazyServices } from "@/components/lazy-services"

export default function Home() {
  const [isVisible, setIsVisible] = useState(false)
  const statsRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 },
    )

    if (statsRef.current) {
      observer.observe(statsRef.current)
    }

    return () => {
      observer.disconnect()
    }
  }, [])

  // Removed heavy framer-motion variants - using CSS-based CardHover instead

  return (
    <div>
      {/* Hero Section with Geometric Shapes */}
      <section>
        <HeroGeometric
          badge="<PERSON> Einstein"
          title1="Know where to find information and how to use it"
          title2="that's the secret of success"
        />
      </section>

      {/* Welcome Section */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <OptimizedImage
                src="/nsb-award.jpeg"
                alt="Professor Nadeem Shafique Butt receiving an award"
                width={500}
                height={400}
                className="rounded-lg shadow-lg"
                priority
                sizes="(max-width: 768px) 100vw, 500px"
              />
            </div>
            <div>
              <h2 className="text-3xl font-bold mb-6">
                Welcome to <span className="text-[#f5a623]">nsbstat.com</span>
              </h2>
              <p className="text-gray-700 mb-4">
                Welcome to the official website of Professor Nadeem Shafique Butt, a distinguished biostatistician with
                extensive experience in teaching, research, and consultancy.
              </p>
              <p className="text-gray-700 mb-4">
                This platform serves as a comprehensive resource for students, researchers, and professionals seeking
                expertise in biostatistics, statistical analysis, and research methodologies.
              </p>
              <p className="text-gray-700 mb-6">
                Explore our courses, research publications, and consultancy services to enhance your understanding of
                statistical concepts and their applications in various fields.
              </p>
              <Link
                href="/contact"
                className="bg-[#0a2158] hover:bg-[#1a3a7a] text-white font-bold py-2 px-6 rounded transition duration-300"
              >
                Get in Touch
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Services Section */}
      <LazyLoadOnScroll>
        <LazyServices />
      </LazyLoadOnScroll>

      {/* Statistics Counter Section */}
      <section className="py-12 bg-[#0a5158]" ref={statsRef}>
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-5 gap-6 text-center">
            <div>
              {isVisible && <LazyCounter end={175} suffix="+" />}
              <p className="text-[#f5a623] font-bold mt-2">Research Papers</p>
            </div>
            <div>
              {isVisible && <LazyCounter end={7500} suffix="+" />}
              <p className="text-[#f5a623] font-bold mt-2">Citations Received</p>
            </div>
            <div>
              {isVisible && <LazyCounter end={30} suffix="+" />}
              <p className="text-[#f5a623] font-bold mt-2">Thesis Supervised</p>
            </div>
            <div>
              {isVisible && <LazyCounter end={22} suffix="+" />}
              <p className="text-[#f5a623] font-bold mt-2">Years of Experience</p>
            </div>
            <div>
              {isVisible && <LazyCounter end={50} suffix="+" />}
              <p className="text-[#f5a623] font-bold mt-2">Workshops Conducted</p>
            </div>
          </div>
        </div>
      </section>

      {/* Recent Courses Section */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Recent Courses</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Course 1 */}
            <CardHover className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="relative h-48">
          <Image src="/biostatistics.jpeg" alt="Biostatistics" fill className="object-cover" />
                <div className="absolute inset-0">
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-3">Biostatistics: Community Medicine</h3>
                <p className="text-gray-600 mb-4 line-clamp-4">
                  Introduction to Biostatistics provides an introduction to selected important topics in biostatistical
                  concepts and reasoning. This course represents an introduction to the field and provides a survey of
                  data and data types.
                </p>
                <LinkWrapper
                  href="/courses/biostatistics"
                  className="text-white bg-[#0a2158] hover:bg-[#1a3a7a] px-4 py-2 rounded inline-block transition duration-300"
                >
                  Read More
                </LinkWrapper>
              </div>
            </CardHover>

            {/* Course 2 */}
            <CardHover className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="relative h-48">
                <Image src="/SurvivalAnalysis.png" alt="Survival Analysis" fill className="object-cover" />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-3">Survival Analysis</h3>
                <p className="text-gray-600 mb-4 line-clamp-4">
                  This course develops and uses statistical methods appropriate for analyzing right-censored (i.e.,
                  incomplete) time-to-event data. Topics covered include nonparametric estimation, Kaplan Meier
                  estimator, nonparametric methods for comparing the survival experience of two or more populations.
                </p>
                <LinkWrapper
                  href="/courses/survival-analysis"
                  className="text-white bg-[#0a2158] hover:bg-[#1a3a7a] px-4 py-2 rounded inline-block transition duration-300"
                >
                  Read More
                </LinkWrapper>
              </div>
            </CardHover>

            {/* Course 3 */}
            <CardHover className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="relative h-48">
                <Image src="/r-logo.png" alt="Statistical Computing" fill className="object-contain bg-white p-2" />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold mb-3">Statistical Computing</h3>
                <p className="text-gray-600 mb-4 line-clamp-4">
                  The purpose of this course is to introduce students to the use of modern statistical packages for
                  analyzing various types of data commonly encountered in many areas of science. Students with limited
                  computer experience will be introduced to some widely used statistical packages.
                </p>
                <LinkWrapper
                  href="/courses/statistical-computing"
                  className="text-white bg-[#0a2158] hover:bg-[#1a3a7a] px-4 py-2 rounded inline-block transition duration-300"
                >
                  Read More
                </LinkWrapper>
              </div>
            </CardHover>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8">Get In Touch</h2>
            <p className="text-center text-gray-600 mb-8">
              Have questions about our courses, research, or consultancy services? Feel free to reach out to us.
            </p>

            <div className="bg-white rounded-lg shadow-lg p-8">
              <LazyLoadOnScroll>
                <LazyContactForm />
              </LazyLoadOnScroll>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
